'use client';

import { useState } from 'react';

interface AIResponse {
  role: 'assistant';
  content: string;
}

export function useAIResponse() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [resumeId, setResumeId] = useState<string | null>(null);

  const setActiveResumeId = (id: string) => {
    setResumeId(id);
  };

  const generateResponse = async (transcript: string): Promise<AIResponse | null> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Sending transcript to API:', transcript);
      console.log('Using resumeId:', resumeId);
      
      const headers: Record<string, string> = { 
        'Content-Type': 'application/json'
      };
      
      // Add resumeId to headers if available
      if (resumeId) {
        headers['x-resume-id'] = resumeId;
      }
      
      const response = await fetch('/api/chatbot', {
        method: 'POST',
        headers,
        body: JSON.stringify({ transcript })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', response.status, errorText);
        throw new Error(`Failed to generate AI response: ${response.status} ${errorText.substring(0, 100)}`);
      }
      
      const data = await response.json();
      console.log('API Response Success:', data);
      return data;
    } catch (err) {
      console.error('API Request Error:', err);
      setError(err instanceof Error ? err : new Error('Failed to generate response'));
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return { generateResponse, isLoading, error, setActiveResumeId };
}