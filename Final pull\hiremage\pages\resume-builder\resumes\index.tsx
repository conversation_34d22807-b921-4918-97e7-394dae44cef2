import { Button } from "@/components/ui/button";
import { PlusSquare } from "lucide-react";
import Link from "next/link";
import dynamic from "next/dynamic";
import { useState, useEffect } from "react";
import apiClient from "@/lib/api-client";
import { ResumeValues } from "@/lib/validation";
import { useToast } from "@/hooks/use-toast";

// Import ResumeItem with client-side only rendering
const ResumeItem = dynamic(() => import("./ResumeItem"), { 
  ssr: false,
  loading: () => (
    <div className="aspect-square rounded-lg bg-gray-100 animate-pulse"></div>
  )
});

export default function ResumesPage() {
  const [isClient, setIsClient] = useState(false);
  const [resumes, setResumes] = useState<ResumeValues[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch resumes on mount
  useEffect(() => {
    const fetchResumes = async () => {
      try {
        setIsLoading(true);
        const data = await apiClient.getResumes();
        setResumes(data);
      } catch (error) {
        console.error("Failed to fetch resumes:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load your resumes. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchResumes();
    setIsClient(true);
  }, [toast]);

  return (
    <main className="mx-auto w-full max-w-7xl space-y-4 px-3 py-4">
      <div className="flex justify-between items-center">
        <div className="space-y-0.5">
          <h1 className="text-2xl font-bold">Your resumes</h1>
          <p className="text-sm text-gray-500">Total: {resumes.length}</p>
        </div>
        <Button asChild className="flex gap-2">
          <Link href="/resume-builder/editor">
            <PlusSquare className="size-4" />
            New resume
          </Link>
        </Button>
      </div>
      
      {isLoading ? (
        // Loading state
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div 
              key={i} 
              className="aspect-square rounded-lg bg-gray-100 animate-pulse"
            />
          ))}
        </div>
      ) : resumes.length === 0 ? (
        // Empty state
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
          <h2 className="text-xl font-semibold">No resumes yet</h2>
          <p className="text-muted-foreground">
            Create your first resume to get started
          </p>
          <Button asChild className="mt-4">
            <Link href="/resume-builder/editor">Create resume</Link>
          </Button>
        </div>
      ) : (
        // List of resumes
        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {isClient && resumes.map((resume) => (
            <ResumeItem key={resume.id} resume={resume} />
          ))}
        </div>
      )}
    </main>
  );
} 