{"name": "tradexpro-admin", "private": true, "version": "1.0.0", "description": "Tradexpro Admin backend with nodejs", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:migrate": "npx prisma migrate dev", "prisma:push": "npx prisma db push", "prisma:seed": "npx prisma db seed", "prisma:generate": "npx prisma generate", "prisma:deploy": "npm run prisma:migrate && npm run prisma:push && npm run prisma:generate"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.772.0", "@aws-sdk/s3-request-presigner": "^3.772.0", "@nestjs/common": "^9.1.4", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.1.4", "@nestjs/jwt": "^9.0.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.1.4", "@nestjs/swagger": "^6.1.2", "@prisma/client": "^4.7.1", "@types/express-handlebars": "^6.0.0", "@types/nodemailer-express-handlebars": "^4.0.5", "axios": "^1.5.1", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "braintree": "^3.17.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^8.2.0", "google-auth-library": "^9.0.0", "handlebars": "^4.7.7", "json-bigint": "^1.0.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.7.0", "nodemailer-express-handlebars": "^6.1.0", "openai": "^4.0.0", "passport": "^0.6.0", "passport-google-oauth2": "^0.2.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma-exclude": "^1.0.2", "razorpay": "^2.9.2", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.1", "sharp": "^0.32.6", "stripe": "^13.5.0", "swagger-ui-express": "^4.1.6", "winston": "^3.3.3"}, "devDependencies": {"@nestjs/cli": "^7.5.6", "@nestjs/schematics": "^7.2.7", "@nestjs/testing": "^7.6.13", "@types/bcrypt": "^3.0.0", "@types/express": "^4.17.11", "@types/jest": "^26.0.20", "@types/json-bigint": "^1.0.1", "@types/multer": "^1.4.7", "@types/node": "^14.14.31", "@types/nodemailer": "^6.4.4", "@types/passport": "^1.0.6", "@types/passport-jwt": "^3.0.5", "@types/passport-local": "^1.0.33", "@types/supertest": "^2.0.10", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-sort-imports-es6-autofix": "^0.6.0", "eslint-plugin-unused-imports": "^1.1.0", "jest": "^26.6.3", "prettier": "^2.2.1", "prisma": "^4.10.1", "supertest": "^6.1.3", "ts-jest": "^26.5.2", "ts-loader": "^8.0.17", "ts-node": "^10.9.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "resolutions": {"string-width": "4.2.3", "strip-ansi": "6.0.1", "wrap-ansi": "7.0.0", "is-fullwidth-code-point": "3.0.0"}}