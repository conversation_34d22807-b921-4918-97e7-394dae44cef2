# Hiremage Backend Kubernetes Deployment

This guide explains how to deploy the Hiremage Backend application to Docker Desktop Kubernetes.

## Prerequisites

- Docker Desktop with Kubernetes enabled
- kubectl CLI installed and configured for Docker Desktop
- Docker image built with `docker build -t hiremage-backend:latest .`

## Deployment Steps

1. **Build the Docker image first:**

```bash
docker build -t hiremage-backend:latest .
```

2. **Apply the Kubernetes manifests:**

```bash
kubectl apply -k kubernetes/
```

3. **Access the application:**

The application will be available at [http://localhost:30000](http://localhost:30000)

## Configuration

### Environment Variables

- Non-sensitive environment variables are stored in `kubernetes/configmap.yaml`
- Sensitive environment variables should be stored in `kubernetes/secret.yaml`
  - The included secret.yaml file contains placeholders
  - Replace with actual Base64-encoded values before applying

### Secrets Setup

To create proper secrets:

1. Edit the `kubernetes/secret.yaml` file
2. For each secret value, Base64 encode it:
   ```bash
   echo -n "your-actual-secret" | base64
   ```
3. Replace the placeholder values in the secret.yaml file
4. Uncomment the secret.yaml line in kustomization.yaml

## Scaling

To scale the deployment to multiple replicas:

```bash
kubectl scale deployment hiremage-backend --replicas=3
```

## Troubleshooting

### View Logs

```bash
kubectl logs -f deployment/hiremage-backend
```

### Check Pod Status

```bash
kubectl get pods -l app=hiremage-backend
```

### Describe the Service

```bash
kubectl describe service hiremage-backend
```

### Restart Deployment

```bash
kubectl rollout restart deployment hiremage-backend
``` 