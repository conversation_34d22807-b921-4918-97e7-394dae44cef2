// public/worklets/pcmWorkletProcessor.js

class PCMWorkletProcessor extends AudioWorkletProcessor {
  process(inputs, outputs, parameters) {
    // We assume single-channel (mono). inputs[0][0] are the float samples
    const inputChannelData = inputs[0][0];
    if (!inputChannelData) return true;

    const len = inputChannelData.length;
    const output = new Int16Array(len);

    for (let i = 0; i < len; i++) {
      // clamp sample to [-1,1], scale to 16-bit range
      const s = Math.max(-1, Math.min(1, inputChannelData[i]));
      output[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
    }

    // Send the 16-bit buffer back to the main thread
    this.port.postMessage(output.buffer);

    return true; // keep processor alive
  }
}

registerProcessor('pcm-worklet-processor', PCMWorkletProcessor);
