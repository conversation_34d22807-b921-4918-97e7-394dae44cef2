-- CreateTable
CREATE TABLE `resume_documents` (
    `id` VARCHAR(191) NOT NULL,
    `fileName` VARCHAR(191) NOT NULL,
    `uploadDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `userId` VARCHAR(191) NOT NULL,
    `s3Key` VARCHAR(191) NOT NULL,
    `s3Bucket` VARCHAR(191) NOT NULL,
    `resumeId` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `resume_vectors` (
    `id` VARCHAR(191) NOT NULL,
    `chunkIndex` INTEGER NOT NULL,
    `fullText` LONGTEXT NOT NULL,
    `vectorEmbedding` LONGTEXT NOT NULL,
    `resumeDocumentId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `resume_vectors` ADD CONSTRAINT `resume_vectors_resumeDocumentId_fkey` FOREIGN KEY (`resumeDocumentId`) REFERENCES `resume_documents`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
