import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ResumeServerData } from "./types";
import { ResumeValues, WorkExperience } from "./validation";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * A replacer function for JSON.stringify to handle File objects
 * This is necessary because File objects can't be directly stringified
 * @param key The key being processed
 * @param value The value for the key
 * @returns A value that can be stringified
 */
export function fileReplacer(key: string, value: any) {
  if (value instanceof File) {
    return '[File]';
  }
  return value;
}

export function mapToResumeValues(data: ResumeServerData): ResumeValues {
  return {
    id: data.id,
    title: data.title || undefined,
    description: data.description || undefined,
    photo: data.photoUrl || undefined,
    firstName: data.firstName || undefined,
    lastName: data.lastName || undefined,
    jobTitle: data.jobTitle || undefined,
    city: data.city || undefined,
    country: data.country || undefined,
    phone: data.phone || undefined,
    email: data.email || undefined,
    workExperiences: (data.workExperiences || []).map((exp: WorkExperience) => ({
      position: exp.position || undefined,
      company: exp.company || undefined,
      startDate: exp.startDate || undefined,
      endDate: exp.endDate || undefined,
      description: exp.description || undefined,
    })),
    educations: (data.educations || []).map((edu: { degree?: string; school?: string; startDate?: string; endDate?: string }) => ({
      degree: edu.degree || undefined,
      school: edu.school || undefined,
      startDate: edu.startDate || undefined,
      endDate: edu.endDate || undefined,
    })),
    skills: data.skills || [],
    borderStyle: data.borderStyle || "solid",
    colorHex: data.colorHex || "#000000",
    summary: data.summary || undefined,
  };
}
