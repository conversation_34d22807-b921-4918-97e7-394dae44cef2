venkata pavan kumar
reddy pathakota
Data Scientist
6 Years 6 Months(+91) 8897506152pavanreddy0918@gma
il.com
Profile Summary
Experienced Data Scientist with 4+ years of expertise in utilizing
data analytics, machine learning, and statistical modeling to
provide valuable insights for business advancement. Skilled in
Python, SQL, and various data science tools. Proven success in
solving intricate problems, refining processes, and effectively
communicating findings to diverse stakeholders. Proficient in
developing predictive models, performing exploratory data
analysis, and creating impactful data visualizations. Dedicated to
transforming data into strategic assets to optimize decision-
making and operational effectiveness.
Education
Work Experience
Mar 2019 - Present
Data Scientist
NEBLIO TECHNOLOGIES PRIVATE LIMITED
Results-driven Data Scientist with over 4 years of experience in
leveraging data analytics, machine learning, and statistical modeling to
deliver actionable insights and drive business growth. Proficient in
Python, SQL, and a variety of data science tools and frameworks.
Demonstrated ability to solve complex problems, optimize processes,
and communicate findings to diverse stakeholders. Proven track record
of developing predictive models, conducting exploratory data analysis,
and creating impactful data visualizations. Passionate about turning
data into strategic assets to inform decision-making and enhance
operational efficiency.
Jun 2017 - Jan 2019
Associate Financial Services Manager
ICICI Prudential Life
Conducted financial analysis and provide client advisory services,
Analyses customer behavior trends to develop targeted marketing
strategies.
Key skills
Python
SQL
Machine Learning
Deep Learning
Deep Learning Frameworks
Tensorflow
Keras
Scikit-Learn
Spacy
generative ai
Computer Vision
Neural Networks
Rnn
Nltk
Cnn
Pandas
Numpy
Power BI
Artificial Intelligence
Statistical Modeling
Statistics
Personal Information
City
Bengaluru
Country
INDIA
Languages
MBA/PGDM, 2014
Sree Vidyanikethan Institute of Management,
Tirupati
Jun 2014 - Dec 2016
Distribution Manager
PROTON REMEDIES PVT LTD
Spearheaded product distribution and marketing strategies, managed
vendor relationships and improved supply chain effectively.
Projects
10 Months
Personalized Travel Recommendations and Target Marketing Campaigns
through Generative AI Analysis
This innovative project leverages cutting-edge generative AI to create
personalized travel recommendations, flight descriptions, and targeted
marketing campaigns. By analyzing extensive user data, including
preferences, behaviors, and past interactions, the system
tailorscontent to individual preferences, thereby significantly
enhancing customer engagement and boosting conversion rates. This
approach not only provides customers with highly relevant and
appealing content but also enables businesses to maximize their
marketing impact and drive revenue growth through more effective
campaigns.
Responsibility:
• Developed and implemented generative AI models forpersonalized
travel recommendations and targeted marketing campaigns.
• Analysed user data to extract meaningful insights andpatterns for
customization.
• Deployed and maintained machine learning models forreal-time
recommendations.
• Built API s and back-end systems to support AI modelintegration and
data processing.
12 Months
Social Sentiment Analysis: Monitoring brand Perception on Social Media
This project involves monitoring social media channels to track
conversations about the retailer's brand, products, and competitors.
Using deep learning algorithms like RNNs, LSTMs, and Transformers
for sentiment analysis, retailers can gauge public perception, identify
trends, and respond to customer queries or complaints in a timely
manner, therebymanaging their brand reputation effectively.
Responsibilities:
• Develop and implement deep learning models, includingRNNs,
LSTMs, and Transformers, for sentiment analysis to accurately gauge
public perception and sentiment towards the retailer's brand.
• Collaborate with cross-functional teams to integrate sentiment
analysis models into a real-time monitoringsystem, enabling timely
responses to customer queries,complaints, and emerging trends.
• Continuously evaluate and optimize model performanceby fine-tuning
algorithms, experimenting with new techniques, and incorporating
feedback from on-going monitoring efforts.
6 Months
Cashier less Stores Using Computer Vision and Deep Learning
This project involves Cashierless stores leverage computer vision and
deep learning technologies to enable self-checkout. These stores
automatically detect product prices and calculate bills as shoppers
select items, improving efficiency and convenience while reducing
checkout times.
Responsibilities:
• Pre-processing and analyzing store image/video data toensure
accuracy and relevance for model training.
English
Telugu
Hindi
• Evaluating the performance of YOLO models (v7 andv8) through
comprehensive metrics assessment.
• Collaborating closely with the Deep Learning Engineerto extract
pertinent features from the data for model enhancement.
• Providing actionable insights derived from data analysisto optimize
object detection algorithms and refine the cashier-less store system.
• Continuously iterating and refining data-driven strategiesto enhance
the overall efficiency and effectiveness of thecashier-less shopping
experience
8 Months
Predicting Customer Churn Retention Strategies for Retailers
This project aims to predict whether a customer is likely to churn
(leave) or not based on theirbehavior, interactions, and transaction
history. By using algorithms like Logistic Regression, Random Forest,
and Gradient Boosting, retailers can proactively identify at-risk
customers and implement targeted retention strategies to reduce churn
rates.
Responsibilities:
• Created data pipelines to collect, clean, and pre-process
customerinteraction data.
• Analyzed churn predictions to develop targeted retentionstrategies.
• Integrated Predictive models into existing systems for real time churn
prediction.
• Implemented and monitored retention strategies based on churn
predictions to improve customer retention rates.
• Conducted feature engineering to identify key behavioural indicators
and transaction patterns that correlate with customerchurn, enhancing
the predictive power of the models.