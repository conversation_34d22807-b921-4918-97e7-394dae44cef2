import { NextApiRequest, NextApiResponse } from 'next';
import { v4 as uuidv4 } from 'uuid';

// Configure this URL based on your backend deployment
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000/v1/chat/';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    const { transcript } = req.body;
    const resumeId = req.headers['x-resume-id'] as string;
    
    if (!transcript) {
      return res.status(400).json({ error: 'Missing transcript in request body' });
    }
    
    console.log('Processing transcript request:', transcript.substring(0, 100) + '...');
    if (resumeId) {
      console.log('Using resumeId for context:', resumeId);
    }
    
    // Generate a unique request ID
    const requestId = uuidv4();
    
    // Set expiry time (1 year from now)
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);
    const expiryTime = expiryDate.toISOString().replace('T', ' ').substring(0, 19);
    
    // Call your backend API
    try {
      const headers: Record<string, string> = {
        'accept': 'application/json',
        'state': 'KA',
        'requestId': requestId,
        'registeredUserId': 'user123',
        'sessionId': `session-${requestId}`,
        'expiryTime': expiryTime,
        'Content-Type': 'application/json'
      };
      
      // Add resumeId to backend request if available
      if (resumeId) {
        headers['x-resume-id'] = resumeId;
      }
      
      const backendResponse = await fetch(BACKEND_API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          question: transcript,
          inputType: 'text'
        })
      });

      if (!backendResponse.ok) {
        const errorText = await backendResponse.text();
        console.error(`Backend error (${backendResponse.status}):`, errorText);
        return res.status(502).json({ 
          error: `Backend service error: ${backendResponse.status}`,
          details: errorText.substring(0, 200)
        });
      }

      const data = await backendResponse.json();
      
      // Temporarily use a mock response if backend data is missing expected fields
      if (!data.answer) {
        console.warn('Backend response missing answer field, using mock response');
        
        // Format a simple response with resumeId reference if available
        const mockResponseText = resumeId 
          ? `I've analyzed your question about the resume (ID: ${resumeId.substring(0, 8)}...): "${transcript.substring(0, 50)}..."`
          : `I've analyzed your question: "${transcript.substring(0, 50)}..."`;
          
        const mockResponse = {
          role: 'assistant',
          content: `${mockResponseText} 
          The answer would typically address key points raised in your interview scenario.
          For best results, please ensure your backend API is properly configured.`
        };
        
        return res.status(200).json(mockResponse);
      }
      
      // Format the response to meet your application's needs
      const response = {
        role: 'assistant',
        content: data.answer
      };

      return res.status(200).json(response);
    } catch (backendError) {
      console.error('Backend connection error:', backendError);
      
      // Return a mock response with resumeId reference if available
      const mockResponseText = resumeId 
        ? `I'm currently unable to process your request about the resume (ID: ${resumeId.substring(0, 8)}...) due to a backend connection issue.`
        : `I'm currently unable to process your request due to a backend connection issue.`;
        
      return res.status(200).json({
        role: 'assistant',
        content: `${mockResponseText}
        Your question was: "${transcript.substring(0, 50)}...". 
        In a real interview scenario, I would provide guidance on how to address this question effectively.`
      });
    }
  } catch (error) {
    console.error('API handler error:', error);
    return res.status(500).json({ error: 'Failed to process request' });
  }
}