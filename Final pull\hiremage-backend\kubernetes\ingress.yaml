apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hiremage-backend-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: hiremage.local  # Add this to your hosts file
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: hiremage-backend
            port:
              number: 3000 