import { ResumeValues } from "@/lib/validation";
import path from "path";
import apiClient from "@/lib/api-client";

export async function saveResume(values: ResumeValues) {
  const { id } = values;

  let photoUrl: string | undefined = undefined;

  // Handle photo upload
  if (values.photo instanceof File) {
    try {
      const filename = `resume-photos/${Date.now()}-${path.basename(values.photo.name)}`;
      
      // Create form data for upload
      const formData = new FormData();
      formData.append('file', values.photo);

      // Upload via simplified API endpoint
      const response = await fetch('/api/s3-upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Upload failed: ${response.status} - ${errorData}`);
      }

      const result = await response.json();
      photoUrl = result.url;
    } catch (error) {
      // Continue with saving even if photo upload fails
    }
  } else if (typeof values.photo === "string") {
    photoUrl = values.photo;
  }

  // Prepare resume data with photoUrl and remove backend-only properties
  const { createdAt, updatedAt, ...cleanValues } = (values as any);
  const resumeData = {
    ...cleanValues,
    photoUrl,
    photo: undefined // Remove photo File object
  };
  
  let result;
  
  try {
    if (id) {
      // Update existing resume
      result = await apiClient.updateResume(id, resumeData);
    } else {
      // Create new resume
      result = await apiClient.createResume(resumeData);
    }
    
    return result;
  } catch (error) {
    throw error;
  }
}
