import type { NextApiRequest, NextApiResponse } from 'next';
import { MongoClient, ObjectId } from 'mongodb';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const client = new MongoClient(process.env.MONGODB_URI ?? '');
  
  try {
    const { id, resumeId } = req.query;
    const userId = req.headers['x-user-id'] || 'default-user';

    if ((!id && !resumeId) || (id && typeof id !== 'string') || (resumeId && typeof resumeId !== 'string')) {
      return res.status(400).json({ error: 'Valid Resume ID or Resume UUID is required' });
    }

    await client.connect();
    
    // Try to find the resume document first to get the S3 Key for deletion
    let resumeDoc = null;
    let s3Key = null;
    let s3Bucket = process.env.AWS_S3_BUCKET || 'hiremage-backend';
    
    // First check in the resume-metadata database
    const resumeMetadataDb = client.db('resume-metadata');
    const resumesCollection = resumeMetadataDb.collection('resumes');
    
    // Try both structures for finding the document
    if (resumeId) {
      resumeDoc = await resumesCollection.findOne({ resumeId: resumeId });
      if (!resumeDoc) {
        resumeDoc = await resumesCollection.findOne({ 'metadata.resumeId': resumeId });
      }
    } else if (id) {
      try {
        resumeDoc = await resumesCollection.findOne({ _id: new ObjectId(id) });
      } catch (e) {
        resumeDoc = await resumesCollection.findOne({ _id: id });
      }
    }
    
    // If not found in resumes collection, try the candidate_data collection
    if (!resumeDoc) {
      const chatbotDb = client.db('chatbot-service-prod');
      const candidateCollection = chatbotDb.collection('candidate_data');
      
      if (resumeId) {
        resumeDoc = await candidateCollection.findOne({ 'metadata.resumeId': resumeId });
      } else if (id) {
        try {
          resumeDoc = await candidateCollection.findOne({ _id: new ObjectId(id) });
        } catch (e) {
          resumeDoc = await candidateCollection.findOne({ _id: id });
        }
      }
    }
    
    // Extract S3 key from the document if found
    if (resumeDoc) {
      s3Key = resumeDoc.s3Key || (resumeDoc.metadata && resumeDoc.metadata.s3Key);
      if (resumeDoc.s3Bucket) {
        s3Bucket = resumeDoc.s3Bucket;
      }
      console.log('Found resume document:', JSON.stringify(resumeDoc));
    } else {
      console.log('Resume document not found in any collection');
    }
    
    // Delete from all collections
    let totalDeleted = 0;
    
    // Delete from resume-metadata.resumes
    let query: any = {}
    if (resumeId) {
      query = { $or: [{ resumeId: resumeId }, { 'metadata.resumeId': resumeId }] };
    } else if (id) {
      try {
        query = { $or: [{ _id: new ObjectId(id) }, { _id: id }] };
      } catch (e) {
        query = { _id: id };
      }
    }
    
    const resumeResult = await resumesCollection.deleteMany(query);
    totalDeleted += resumeResult.deletedCount;
    
    // Delete from chatbot-service-prod.candidate_data
    const chatbotDb = client.db('chatbot-service-prod');
    const candidateCollection = chatbotDb.collection('candidate_data');
    
    query = {}
    if (resumeId) {
      query = { 'metadata.resumeId': resumeId };
    } else if (id) {
      try {
        query = { _id: new ObjectId(id) };
      } catch (e) {
        query = { _id: id };
      }
    }
    
    const candidateResult = await candidateCollection.deleteMany(query);
    totalDeleted += candidateResult.deletedCount;
    
    // If nothing was deleted from either collection
    if (totalDeleted === 0) {
      return res.status(404).json({ error: 'Resume not found or already deleted' });
    }
    
    // If we have an S3 key, delete the file from S3
    if (s3Key) {
      try {
        // Get S3 configuration
        const region = process.env.AWS_REGION || 'ap-south-1';
        const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
        const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
        
        if (accessKeyId && secretAccessKey) {
          const s3Client = new S3Client({
            region,
            credentials: {
              accessKeyId,
              secretAccessKey,
            },
          });
          
          await s3Client.send(new DeleteObjectCommand({
            Bucket: s3Bucket,
            Key: s3Key,
          }));
          
          console.log(`Deleted S3 object: ${s3Bucket}/${s3Key}`);
        }
      } catch (s3Error) {
        console.error('Error deleting from S3:', s3Error);
        // Continue even if S3 deletion fails
      }
    }

    return res.status(200).json({ 
      message: 'Resume deleted successfully',
      deletedCount: totalDeleted
    });
  } catch (err: any) {
    console.error('Error deleting resume:', err);
    return res.status(500).json({ 
      error: 'Failed to delete resume',
      details: err.message 
    });
  } finally {
    await client.close();
  }
} 