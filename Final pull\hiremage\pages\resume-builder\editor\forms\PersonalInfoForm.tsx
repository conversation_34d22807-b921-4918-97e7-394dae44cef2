"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { EditorFormProps } from "@/lib/types";
import { personalInfoSchema, PersonalInfoValues } from "@/lib/validation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useRef } from "react";
import { useForm } from "react-hook-form";

export default function PersonalInfoForm({
  resumeData = {},
  setResumeData = () => {},
}: EditorFormProps) {
  const form = useForm<PersonalInfoValues>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: resumeData?.firstName || "",
      lastName: resumeData?.lastName || "",
      jobTitle: resumeData?.jobTitle || "",
      city: resumeData?.city || "",
      country: resumeData?.country || "",
      phone: resumeData?.phone || "",
      email: resumeData?.email || "",
    },
  });

  useEffect(() => {
    if (typeof window === 'undefined') return () => {}; // Don't run on server
    
    // Only watch text fields to avoid circular reference with File objects
    const { unsubscribe } = form.watch((values: any) => {
      // Skip automatic validation to avoid circular reference issues
      const textFieldsOnly = {
        firstName: values.firstName,
        lastName: values.lastName,
        jobTitle: values.jobTitle,
        city: values.city,
        country: values.country,
        phone: values.phone,
        email: values.email,
      };
      
      // Update only if values have changed
      setResumeData({ ...resumeData, ...textFieldsOnly });
    });
    return unsubscribe;
  }, [form, resumeData, setResumeData]);

  const photoInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="mx-auto w-full max-w-2xl space-y-6 p-2">
      <div className="space-y-1.5 text-center">
        <h2 className="text-2xl font-semibold">Personal info</h2>
        <p className="text-sm text-muted-foreground">Tell us about yourself.</p>
      </div>
      <Form {...form}>
        <form className="space-y-4">
          <FormField
            control={form.control}
            name="photo"
            render={({ field: { value, ...fieldValues } }: any) => (
              <FormItem className="bg-secondary/50 p-3 rounded-md">
                <FormLabel className="text-base">Your photo</FormLabel>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                  <FormControl>
                    <Input
                      {...fieldValues}
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        console.log("Selected file:", file ? {
                          name: file.name,
                          type: file.type,
                          size: file.size
                        } : null);
                        
                        fieldValues.onChange(file);
                        
                        if (file) {
                          console.log("Updating resumeData with new photo file");
                          setResumeData({
                            ...resumeData,
                            photo: file,
                            photoUrl: undefined
                          });
                        }
                      }}
                      ref={photoInputRef}
                      className="w-full"
                    />
                  </FormControl>
                  <Button
                    variant="secondary"
                    type="button"
                    onClick={() => {
                      console.log("Removing photo");
                      fieldValues.onChange(null);
                      setResumeData({
                        ...resumeData,
                        photo: null,
                        photoUrl: undefined
                      });
                      if (photoInputRef.current) {
                        photoInputRef.current.value = "";
                      }
                    }}
                    className="whitespace-nowrap"
                  >
                    Remove
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel className="text-base">First name</FormLabel>
                  <FormControl>
                    <Input {...field} className="h-10" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel className="text-base">Last name</FormLabel>
                  <FormControl>
                    <Input {...field} className="h-10" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="jobTitle"
            render={({ field }: any) => (
              <FormItem>
                <FormLabel className="text-base">Job title</FormLabel>
                <FormControl>
                  <Input {...field} className="h-10" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel className="text-base">City</FormLabel>
                  <FormControl>
                    <Input {...field} className="h-10" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="country"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel className="text-base">Country</FormLabel>
                  <FormControl>
                    <Input {...field} className="h-10" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="phone"
            render={({ field }: any) => (
              <FormItem>
                <FormLabel className="text-base">Phone</FormLabel>
                <FormControl>
                  <Input {...field} type="tel" className="h-10" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }: any) => (
              <FormItem>
                <FormLabel className="text-base">Email</FormLabel>
                <FormControl>
                  <Input {...field} type="email" className="h-10" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
}
