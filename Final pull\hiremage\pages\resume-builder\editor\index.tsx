import { useRouter } from "next/router";
import { useEffect, useState, useRef } from "react";
import dynamic from "next/dynamic";
import { ResumeValues } from "@/lib/validation";
import apiClient from "@/lib/api-client";
import { useToast } from "@/hooks/use-toast";

// Import the ResumeEditor component with client-side only rendering
const ResumeEditor = dynamic(() => import("./ResumeEditor"), { 
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-screen">Loading...</div>
});

export default function EditorPage() {
  const router = useRouter();
  const { resumeId } = router.query;
  const [resumeToEdit, setResumeToEdit] = useState<ResumeValues | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const loadedResumeId = useRef<string | null>(null);

  useEffect(() => {
    // Only fetch resume if resumeId is provided, it's a string, and we haven't loaded this resume before
    if (resumeId && typeof resumeId === 'string' && resumeId !== loadedResumeId.current) {
      const fetchResume = async () => {
        try {
          setIsLoading(true);
          const data = await apiClient.getResumeById(resumeId);
          setResumeToEdit(data);
          // Store the ID we just loaded to prevent reloading
          loadedResumeId.current = resumeId;
        } catch (error: any) {
          console.error("Failed to fetch resume:", error);
          
          // If the error mentions UUID, this likely means we have a non-UUID ID format
          // In this case, start with a fresh resume but maintain the ID
          if (error.message && error.message.includes('UUID')) {
            setResumeToEdit({ id: resumeId } as ResumeValues);
            loadedResumeId.current = resumeId;
            toast({
              variant: "default",
              title: "Continuing with new resume",
              description: "Using the provided ID for a new resume.",
            });
          } else {
            toast({
              variant: "destructive",
              title: "Error",
              description: "Failed to load resume data. Please try again.",
            });
            
            // Redirect to create a new resume
            router.push('/resume-builder/editor');
          }
        } finally {
          setIsLoading(false);
        }
      };

      fetchResume();
    }
  }, [resumeId, toast, router]);

  // Show loading state while fetching resume
  if (resumeId && isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading resume data...</div>;
  }

  return <ResumeEditor resumeToEdit={resumeToEdit} />;
} 