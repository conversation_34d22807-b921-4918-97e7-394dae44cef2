import {
  <PERSON>,
  Get,
  Param,
  Post,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ResponseModel } from 'src/shared/models/response.model';

import { FileInterceptor } from '@nestjs/platform-express';
import { Public } from 'src/shared/decorators/public.decorator';
import path from 'path';
import { multerUploadConfig } from 'src/shared/configs/multer-upload.config';
import { s3UploadConfig } from 'src/shared/configs/s3-upload.config';
import { Response } from 'express';
import { FilesService } from './files.service';
import { UserInfo } from 'src/shared/decorators/user.decorators';
import { User } from '@prisma/client';
import { coreConstant } from 'src/shared/helpers/coreConstant';

@Controller('file')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file', s3UploadConfig))
  uploadFile(@UploadedFile() file: Express.Multer.File) {
    console.log(file, 'file');
    return this.filesService.createUploadResponse(file);
  }

  @Get('my-uploaded-images')
  myUploadedImages(@UserInfo() user: User) {
    return this.filesService.getMyUploadedFiles(user);
  }

  @Get(':filename')
  @Public()
  async serveFile(@Param('filename') filename: string, @Res() res: Response) {
    // Use the file service to handle serving files from different storage locations
    return this.filesService.serveFile(filename, res);
  }
}
