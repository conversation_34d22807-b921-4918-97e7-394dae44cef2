.root-loader-css {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 100;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.section-loader-css {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  padding: 50px;
}
.root-loader-css:after,
.section-loader-css:after {
  content: " ";
  display: block;
  border-radius: 50%;
  width: 0;
  height: 0;
  margin: 8px;
  box-sizing: border-box;
  border: 32px solid #1abc9c;
  border-color: #1abc9c transparent #1abc9c transparent;
  animation: loader-css-animate 1.2s infinite;
}
@keyframes loader-css-animate {
  0% {
    transform: rotate(0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  50% {
    transform: rotate(900deg);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  100% {
    transform: rotate(1800deg);
  }
}
