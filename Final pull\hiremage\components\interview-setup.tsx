'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { toast } from 'react-toastify';

// Example role & mode data


interface InterviewSetupProps {
  open: boolean;
  onClose: () => void;
  onStart: () => void;
}

/**
 * InterviewSetup Wizard:
 * Upload user's resume file
 * Then POST to /api/storeResumeS3.
 */
export default function InterviewSetup({ open, onClose, onStart }: InterviewSetupProps) {
  // File state
  const [resumeFile, setResumeFile] = useState<File | null>(null);

  // Upload/loading states
  const [isUploading, setIsUploading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');

  // Reset states when dialog is opened
  useEffect(() => {
    if (open) {
      setResumeFile(null);
      setErrorMsg('');
      setIsUploading(false);
    }
  }, [open]);

  // -------------------------
  // Handle file
  // -------------------------
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      const file = e.target.files[0];
      const allowedExtensions = ['pdf', 'doc', 'docx'];
      const ext = file.name.split('.').pop()?.toLowerCase();
      if (!ext || !allowedExtensions.includes(ext)) {
        alert('Invalid file type. Please upload a PDF, DOC, or DOCX file.');
        e.target.value = '';
        return;
      }
      setResumeFile(file);
      setErrorMsg(''); // clear any previous errors
    }
  };

  // -------------------------
  // Upload logic
  // -------------------------
  const handleUpload = async () => {
    setErrorMsg('');

    try {
      if (!resumeFile) {
        setErrorMsg('Please upload a file first.');
        return;
      }

      setIsUploading(true);

      // Prepare FormData
      const formData = new FormData();
      formData.append('file', resumeFile);

      // POST to our new S3 upload endpoint
      const res = await fetch('/api/storeResumeS3', {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) {
        const errorData = await res.json().catch(() => null);
        throw new Error(errorData?.error || `Upload failed: ${res.statusText}`);
      }

      // Get response from server
      const data = await res.json();
      console.log('Resume uploaded to S3 successfully:', data);

      // Show success toast
      toast.success('Successfully Uploaded!', {
        position: "top-center",
        autoClose: 2000,
      });

      // Close the dialog first
      onClose();
      // Wait for dialog to close and toast to show before starting session
      setTimeout(() => {
        onStart();
      }, 1000);

    } catch (err: any) {
      console.error('Failed to upload resume:', err);
      setErrorMsg(err.message || 'Failed to upload resume. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // -------------------------
  // Rendering
  // -------------------------
  return (
    <Dialog 
      open={open} 
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          onClose();
        }
      }}
    >
      <DialogContent 
        className="sm:max-w-[425px] border shadow-lg z-[100] bg-white dark:bg-black"
      >
        <DialogHeader>
          <DialogTitle>Upload Resume</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="space-y-4">
            <Label htmlFor="resume">Upload Your Resume</Label>
            <Input
              id="resume"
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={handleFileChange}
            />
            <p className="text-sm text-muted-foreground">
              Supported formats: PDF, DOC, DOCX.
            </p>
          </div>
        </div>

        {errorMsg && (
          <p className="text-red-500 text-sm mb-2">
            {errorMsg}
          </p>
        )}

        <div className="flex justify-end items-center">
          <Button
            className="w-full"
            onClick={handleUpload}
            disabled={!resumeFile || isUploading}
          >
            {isUploading ? 'Uploading...' : 'Upload'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
