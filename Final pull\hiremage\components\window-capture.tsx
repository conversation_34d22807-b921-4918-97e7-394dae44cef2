'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from './ui/button';
import { Monitor, StopCircle } from 'lucide-react';

interface WindowCaptureProps {
  /**
   * onTranscript is called whenever we have a new transcription
   * after a pause of 1.5 seconds
   *
   * The callback receives the raw text of the transcript
   */
  onTranscript?: (text: string) => void;
}

export default function WindowCapture({ onTranscript }: WindowCaptureProps) {
  // Store final transcripts here
  const [finalTranscript, setFinalTranscript] = useState('');
  // Store partial transcript text here
  const [partialTranscript, setPartialTranscript] = useState('');
  
  // Streaming + error states
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // AssemblyAI token handling
  const [token, setToken] = useState<string | null>(null);
  const [isTokenFetched, setIsTokenFetched] = useState<boolean>(false);

  // Refs for media + audio processing
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);
  const websocketRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | null>(null);
  // Add a reference for the transcription container
  const transcriptionRef = useRef<HTMLDivElement>(null);
  
  // Pause detection
  const lastTranscriptRef = useRef<string>('');
  const transcriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /* ------------------------------------------------------------------
   * 1) Fetch AssemblyAI token once on mount
   * ----------------------------------------------------------------*/
  useEffect(() => {
    fetchToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchToken = async () => {
    try {
      // Adjust this path or logic to match your server's token-generation API
      const response = await fetch('/api/generateToken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ expires_in: 3600 }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate token');
      }

      const data = await response.json();
      if (!data.token) {
        throw new Error('Token not present in response');
      }
      console.log('Fetched token:', data.token);

      setToken(data.token);
      setIsTokenFetched(true);
    } catch (err) {
      console.error('Failed to fetch token:', err);
      setError('Failed to fetch token. Please try again.');
      setIsTokenFetched(false);
    }
  };

  /* ------------------------------------------------------------------
   * 2) Start screen capture (video + system audio)
   * ----------------------------------------------------------------*/
  const startCapture = async () => {
    if (!isTokenFetched || !token) {
      console.error('Token is not ready yet');
      setError('Token is not available. Please wait or refresh the page.');
      return;
    }
    setError(null);

    try {
      // Request screen + system audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true,
      });

      // Show the captured screen locally
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      audioStreamRef.current = stream;

      // Kick off AssemblyAI real-time transcription
      startTranscription();
    } catch (err) {
      console.error('Error starting capture:', err);
      setError('Failed to start capture. Please allow permissions.');
    }
  };

  /* ------------------------------------------------------------------
   * 3) Stop screen capture
   * ----------------------------------------------------------------*/
  const stopCapture = () => {
    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach((track) => track.stop());
    }
    stopTranscription();
  };

  /* ------------------------------------------------------------------
   * 4) Start transcription (WebSocket -> AssemblyAI -> Realtime)
   * ----------------------------------------------------------------*/
  const startTranscription = async () => {
    console.log('Using token:', token);
    try {
      // We'll use 16k sample rate, PCM 16-bit LE encoding
      const sampleRate = 16000;
      const encoding = 'pcm_s16le';

      // Build the WebSocket URL
      const wsUrl = `wss://api.assemblyai.com/v2/realtime/ws?token=${token}&sample_rate=${sampleRate}&encoding=${encoding}`;
      const ws = new WebSocket(wsUrl);
      websocketRef.current = ws;
      setIsStreaming(true);

      // ----------------------- WebSocket Handlers -----------------------
      ws.onopen = () => {
        console.log('WebSocket connection established');
      };

      ws.onerror = (err) => {
        console.error('WebSocket error:', err);
        setError('WebSocket error occurred. Please try again.');
        stopTranscription();
      };

      ws.onclose = (event) => {
        console.log('WebSocket connection closed:', event.code, event.reason);
        setIsStreaming(false);
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          const msgType = message.message_type || '';

          // "PartialTranscript" -> show user partial text
          if (msgType === 'PartialTranscript') {
            setPartialTranscript(message.text || '');
          }
          // "FinalTranscript" -> finalize text
          else if (msgType === 'FinalTranscript') {
            const newText = message.text || '';
            setFinalTranscript((prev) => (prev ? `${prev} ${newText}` : newText));
            setPartialTranscript('');

            // Handle transcript with pause detection
            handleTranscriptWithPause(newText);
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      // -------------------- AudioContext + Worklet ---------------------
      const audioContext = new AudioContext({ sampleRate });
      audioContextRef.current = audioContext;

      // Load the custom AudioWorklet Processor that encodes audio as PCM
      await audioContext.audioWorklet.addModule('/worklets/pcmWorkletProcessor.js');

      // Create a source from the captured audio stream
      const source = audioContext.createMediaStreamSource(audioStreamRef.current!);

      // Create the AudioWorkletNode
      const workletNode = new AudioWorkletNode(audioContext, 'pcm-worklet-processor');
      workletNodeRef.current = workletNode;

      // The processor sends 16-bit PCM chunks back to JS
      workletNode.port.onmessage = (e: MessageEvent) => {
        const pcmBuffer = e.data as ArrayBuffer;
        // Forward PCM data to AssemblyAI if the WebSocket is open
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(pcmBuffer);
        }
      };

      // Connect source -> PCM worklet
      source.connect(workletNode);

      // Some browsers require us to connect the node to an output to keep it alive
      workletNode.connect(audioContext.destination);

    } catch (err) {
      console.error('Failed to start transcription:', err);
      setError('Failed to start transcription.');
    }
  };

  /* ------------------------------------------------------------------
   * 5) Stop transcription
   * ----------------------------------------------------------------*/
  const stopTranscription = () => {
    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = null;
    }
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    workletNodeRef.current = null;
    setIsStreaming(false);
    
    // Clear any pending timeouts
    if (transcriptionTimeoutRef.current) {
      clearTimeout(transcriptionTimeoutRef.current);
      transcriptionTimeoutRef.current = null;
    }
  };

  /* ------------------------------------------------------------------
   * 6) Handle transcript with pause detection
   * ----------------------------------------------------------------*/
  const handleTranscriptWithPause = (newText: string) => {
    // Update current transcript
    const currentFullTranscript = finalTranscript + (finalTranscript ? ' ' : '') + newText;
    lastTranscriptRef.current = currentFullTranscript;
    
    // Clear any existing timeout
    if (transcriptionTimeoutRef.current) {
      clearTimeout(transcriptionTimeoutRef.current);
    }
    
    // Set a new timeout to send the transcription after 1.5 seconds of silence
    transcriptionTimeoutRef.current = setTimeout(() => {
      if (lastTranscriptRef.current.trim() && onTranscript) {
        console.log('Pause detected - sending transcript:', lastTranscriptRef.current);
        onTranscript(lastTranscriptRef.current);
      }
    }, 1500); // 1.5 seconds pause
  };

  /* ------------------------------------------------------------------
   * 7) Cleanup if the component unmounts
   * ----------------------------------------------------------------*/
  useEffect(() => {
    return () => {
      stopCapture();
      stopTranscription();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* ------------------------------------------------------------------
   * 8) Button animation via direct DOM
   * ----------------------------------------------------------------*/
  const btnRef = useRef<HTMLButtonElement>(null);
  
  useEffect(() => {
    // Skip animation if streaming or button not rendered
    if (isStreaming || !btnRef.current) return;
    
    let animationFrame: number;
    let startTime: number | null = null;
    const duration = 3000; // 3 seconds for a full cycle
    
    // Animation function
    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = (elapsed % duration) / duration;
      
      // Calculate Y position (float up and down)
      const yOffset = Math.sin(progress * Math.PI * 2) * 10; // 10px up and down
      
      // Calculate glow intensity
      const glowIntensity = (Math.sin(progress * Math.PI * 4) + 1) / 2; // 0 to 1
      const glowSize = 10 + (glowIntensity * 10); // 10-20px
      
      // Apply transformations if button exists
      if (btnRef.current) {
        btnRef.current.style.transform = `translateY(${-yOffset}px)`;
        btnRef.current.style.boxShadow = `0 0 ${glowSize}px ${glowIntensity * 10}px rgba(79, 70, 229, ${0.3 + (glowIntensity * 0.4)})`;
      }
      
      // Continue animation loop
      animationFrame = requestAnimationFrame(animate);
    };
    
    // Start animation
    animationFrame = requestAnimationFrame(animate);
    
    // Clean up
    return () => {
      cancelAnimationFrame(animationFrame);
      if (btnRef.current) {
        btnRef.current.style.transform = '';
        btnRef.current.style.boxShadow = '';
      }
    };
  }, [isStreaming]);

  // Auto-scroll for transcription when it updates
  useEffect(() => {
    if (transcriptionRef.current) {
      transcriptionRef.current.scrollTop = transcriptionRef.current.scrollHeight;
    }
  }, [finalTranscript, partialTranscript]);

  // Function to clear transcription text
  const clearTranscription = () => {
    setFinalTranscript('');
    setPartialTranscript('');
    if (transcriptionTimeoutRef.current) {
      clearTimeout(transcriptionTimeoutRef.current);
      transcriptionTimeoutRef.current = null;
    }
  };

  /* ------------------------------------------------------------------
   * 9) Render UI
   * ----------------------------------------------------------------*/
  return (
    <div className="p-2">
      {/* Video preview of the captured screen with button inside */}
      <div className="mt-2 mx-2 relative">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full aspect-video bg-secondary rounded-md"
        />
        
        {/* Button positioned inside the video screen with animation */}
        <div className="absolute inset-0 flex items-center justify-center">
          {!isStreaming && (
            <div className="transition-all duration-500 ease-in-out transform">
              <Button 
                ref={btnRef}
                onClick={startCapture} 
                disabled={!isTokenFetched} 
                size="lg"
                className="shadow-lg bg-primary hover:bg-primary/90"
                style={{ transition: 'transform 0.05s linear, box-shadow 0.05s linear' }}
              >
                <Monitor className="mr-2 h-5 w-5" />
                {isTokenFetched ? 'Connect to Meeting Room' : 'Preparing connection...'}
              </Button>
            </div>
          )}
        </div>
        
        {/* Small stop button that appears when streaming */}
        {isStreaming && (
          <div className="absolute bottom-4 right-4 transition-all duration-300 transform">
            <Button 
              variant="destructive" 
              onClick={stopCapture} 
              size="icon"
              className="shadow-lg opacity-80 hover:opacity-100 h-6 w-6 rounded-full flex items-center justify-center border-2 border-white"
            >
              <StopCircle className="h-4 w-4 text-red-500 fill-red-500" />
            </Button>
          </div>
        )}
      </div>

      {/* Raw Transcription */}
      <div className="mt-3 mx-2">
        <details open>
          <summary className="text-sm font-medium cursor-pointer mb-1">Raw Transcription</summary>
          <div 
            ref={transcriptionRef}
            className="bg-gray-50 p-3 rounded-md h-48 overflow-y-auto text-sm"
          >
            {(finalTranscript || partialTranscript) ? (
              <>
                {finalTranscript}
                {partialTranscript && (
                  <em className="text-gray-500 ml-1">{partialTranscript}</em>
                )}
              </>
            ) : (
              <p className="text-gray-500">No transcription available yet.</p>
            )}
          </div>
          <div className="flex justify-end mt-2">
            <button
              onClick={clearTranscription}
              className="text-xs text-red-500 hover:text-red-700 px-2 py-1 rounded flex items-center gap-1 hover:bg-red-50 transition-colors"
              disabled={!finalTranscript && !partialTranscript}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
              Clear Transcription
            </button>
          </div>
        </details>
      </div>

      {/* Error Display (if any) */}
      {error && <p className="text-red-500 mt-2 mx-2">{error}</p>}
    </div>
  );
}

// Add the styles to the document head
if (typeof document !== 'undefined') {
  const styleEl = document.createElement('style');
  styleEl.textContent = `
    /* Hide footer when in full-page interview mode */
    body.full-page-interview footer {
      display: none !important;
    }
    
    /* Also hide any elements with copyright text in interview mode */
    body.full-page-interview .copyright,
    body.full-page-interview [class*="copyright"],
    body.full-page-interview [class*="footer"] {
      display: none !important;
    }
  `;
  document.head.appendChild(styleEl);
}
