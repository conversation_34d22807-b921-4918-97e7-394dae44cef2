apiVersion: v1
kind: Secret
metadata:
  name: hiremage-backend-secrets
type: Opaque
data:
  # These values need to be base64 encoded
  # Use: echo -n "actual-value" | base64
  
  # Database password
  DB_PASSWORD: "Qml0aWRlYSQyMDI1"  # base64 encoded "Bitidea$2025"
  
  # Authentication tokens
  ACCESS_TOKEN_SECRET: "YXNtZGxhbXNsa2RtYWxrc2Rt"  # base64 encoded "asmdlamslkdmalksdm"
  REFRESH_TOKEN_SECRET: "YXNsbWxtZHNsbWFsc2RrbWpmbmFqa3NkbmprYXM="  # base64 encoded "aslmlmdslmalsdkmjfnajksdnjkas"
  
  # AWS credentials
  AWS_ACCESS_KEY_ID: "QUtJQVhVRVJYQldQUVE0WEpXR0k="  # base64 encoded "********************"
  AWS_SECRET_ACCESS_KEY: "dlowU2ZlZytVUzBrOVRsRTVEV1hlWkNKUmV3cWRWdUxPNHM3MHlqaQ==" # base64 encoded "vZ0Sfeg+US0k9TlE5DWXeZCJRewqdVuLO4s70yji" 