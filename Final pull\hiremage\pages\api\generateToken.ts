// File: pages/api/generateToken.ts
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { expires_in } = req.body;

  if (!expires_in || expires_in < 60) {
    return res.status(400).json({ error: 'Invalid or missing expires_in value. Must be >= 60.' });
  }

  const ASSEMBLYAI_API_KEY = process.env.ASSEMBLYAI_API_KEY;

  if (!ASSEMBLYAI_API_KEY) {
    return res.status(500).json({ error: 'AssemblyAI API key is not configured in environment variables.' });
  }

  try {
    const response = await fetch('https://api.assemblyai.com/v2/realtime/token', {
      method: 'POST',
      headers: {
        Authorization: ASSEMBLYAI_API_KEY, // Direct API key without 'Bearer'
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ expires_in }),
    });

    if (!response.ok) {
      const error = await response.json();
      return res.status(response.status).json({ error: error.message });
    }

    const data = await response.json();
    res.status(200).json(data);
  } catch (error: any) {
    res.status(500).json({ error: 'Failed to generate token.' });
  }
}
