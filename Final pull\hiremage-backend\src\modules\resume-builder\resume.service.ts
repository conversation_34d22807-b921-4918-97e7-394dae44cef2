import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateResumeDto, UpdateResumeDto } from './dto/resume.dto';

@Injectable()
export class ResumeService {
  constructor(private prisma: PrismaService) {}

  async getAllResumes() {
    const resumes = await this.prisma.resume.findMany({
      include: {
        workExperiences: true,
        educations: true,
      },
    });

    return resumes.map(resume => ({
      ...resume,
      skills: this.parseSkills(resume.skills),
    }));
  }

  async getResumeById(id: string) {
    const resume = await this.prisma.resume.findUnique({
      where: { id },
      include: {
        workExperiences: true,
        educations: true,
      },
    });

    if (!resume) {
      throw new NotFoundException(`Resume with ID ${id} not found`);
    }

    return {
      ...resume,
      skills: this.parseSkills(resume.skills),
    };
  }

  async createResume(data: CreateResumeDto) {
    const { workExperiences, educations, skills, ...resumeData } = data;

    const resume = await this.prisma.resume.create({
      data: {
        ...resumeData,
        skills: skills ? JSON.stringify(skills) : null,
        workExperiences: {
          create: workExperiences?.map(exp => ({
            position: exp.position,
            company: exp.company,
            startDate: exp.startDate ? new Date(exp.startDate) : null,
            endDate: exp.endDate ? new Date(exp.endDate) : null,
            description: exp.description,
          })) || [],
        },
        educations: {
          create: educations?.map(edu => ({
            degree: edu.degree,
            school: edu.school,
            startDate: edu.startDate ? new Date(edu.startDate) : null,
            endDate: edu.endDate ? new Date(edu.endDate) : null,
          })) || [],
        },
      },
      include: {
        workExperiences: true,
        educations: true,
      },
    });

    return {
      ...resume,
      skills: this.parseSkills(resume.skills),
    };
  }

  async updateResume(id: string, data: UpdateResumeDto) {
    // Check if resume exists
    await this.getResumeById(id);

    const { workExperiences, educations, skills, ...resumeData } = data;

    try {
      // Update the resume and related data
      const result = await this.prisma.$transaction(async (tx) => {
        // Update main resume data
        const updatedResume = await tx.resume.update({
          where: { id },
          data: {
            ...resumeData,
            skills: skills ? JSON.stringify(skills) : undefined,
          },
        });

        // Handle work experiences
        if (workExperiences) {
          // Get IDs of work experiences that should be kept
          const workExpIds = workExperiences
            .filter(exp => exp.id)
            .map(exp => exp.id);

          // Delete work experiences that aren't in the update
          if (workExpIds.length > 0) {
            await tx.workExperience.deleteMany({
              where: {
                resumeId: id,
                id: { notIn: workExpIds },
              },
            });
          } else {
            // If no IDs provided, delete all existing work experiences
            await tx.workExperience.deleteMany({
              where: {
                resumeId: id,
              },
            });
          }

          // Update or create work experiences
          for (const exp of workExperiences) {
            if (exp.id) {
              await tx.workExperience.update({
                where: { id: exp.id },
                data: {
                  position: exp.position,
                  company: exp.company,
                  startDate: exp.startDate ? new Date(exp.startDate) : null,
                  endDate: exp.endDate ? new Date(exp.endDate) : null,
                  description: exp.description,
                },
              });
            } else {
              await tx.workExperience.create({
                data: {
                  position: exp.position,
                  company: exp.company,
                  startDate: exp.startDate ? new Date(exp.startDate) : null,
                  endDate: exp.endDate ? new Date(exp.endDate) : null,
                  description: exp.description,
                  resumeId: id,
                },
              });
            }
          }
        }

        // Handle educations
        if (educations) {
          // Get IDs of educations that should be kept
          const eduIds = educations
            .filter(edu => edu.id)
            .map(edu => edu.id);

          // Delete educations that aren't in the update
          if (eduIds.length > 0) {
            await tx.education.deleteMany({
              where: {
                resumeId: id,
                id: { notIn: eduIds },
              },
            });
          } else {
            // If no IDs provided, delete all existing educations
            await tx.education.deleteMany({
              where: {
                resumeId: id,
              },
            });
          }

          // Update or create educations
          for (const edu of educations) {
            if (edu.id) {
              await tx.education.update({
                where: { id: edu.id },
                data: {
                  degree: edu.degree,
                  school: edu.school,
                  startDate: edu.startDate ? new Date(edu.startDate) : null,
                  endDate: edu.endDate ? new Date(edu.endDate) : null,
                },
              });
            } else {
              await tx.education.create({
                data: {
                  degree: edu.degree,
                  school: edu.school,
                  startDate: edu.startDate ? new Date(edu.startDate) : null,
                  endDate: edu.endDate ? new Date(edu.endDate) : null,
                  resumeId: id,
                },
              });
            }
          }
        }

        // Return the updated resume with all related data
        return tx.resume.findUnique({
          where: { id },
          include: {
            workExperiences: true,
            educations: true,
          },
        });
      });

      return {
        ...result,
        skills: this.parseSkills(result.skills),
      };
    } catch (error) {
      // Log the error for debugging
      console.error('Error in updateResume transaction:', error);
      throw error;
    }
  }

  async deleteResume(id: string) {
    // Check if resume exists
    await this.getResumeById(id);

    // Delete the resume (cascade delete will handle related items)
    return this.prisma.resume.delete({
      where: { id },
    });
  }

  // Helper method to parse skills from JSON string to array
  private parseSkills(skills: string | null): string[] {
    if (!skills) return [];
    
    try {
      return JSON.parse(skills);
    } catch (err) {
      return [];
    }
  }
} 