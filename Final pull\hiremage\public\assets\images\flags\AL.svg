<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>AL</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#EE343C" offset="0%"></stop>
            <stop stop-color="#E2222A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#0D0D0D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="AL">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <path d="M9.34790039,3.34790039 L10.1520996,4.15209961 C10.3394737,4.33947372 10.6557603,4.34423969 10.8479004,4.15209961 L11.6520996,3.34790039 C11.8394737,3.16052628 12.1835102,3.12234016 12.4098816,3.27325439 L13.5,4 L11.9520178,4.77399111 C11.7034302,4.89828491 11.5,5.22385763 11.5,5.5 C11.5,5.76806641 11.7238576,6 12,6 C12.2680664,6 12.6987251,5.90063747 12.9438648,5.77806759 L14.0561352,5.22193241 C14.3009205,5.09953976 14.6565891,5.15658907 14.8497515,5.34975147 L15.1502485,5.65024853 C15.34375,5.84375 15.3012749,6.09936253 15.0561352,6.22193241 L13.9438648,6.77806759 C13.6990795,6.90046024 13.6835102,7.12234016 13.9098816,7.27325439 L14.5901184,7.72674561 C14.8160706,7.87738037 14.78419,8.043162 14.5179749,8.09640503 L12.9820251,8.40359497 C12.7131486,8.45737028 12.6812037,8.63590279 12.9047298,8.80354738 L14.0952702,9.69645262 C14.3154297,9.86157227 14.2761424,10 14,10 C13.7319336,10 13.2832413,9.94581031 13.0158558,9.87896395 L11.9841442,9.62103605 C11.714035,9.55350876 11.6223402,9.68351024 11.7732544,9.90988159 L12.2267456,10.5901184 C12.3773804,10.8160706 12.2726361,11 11.9921684,11 L11.5078316,11 C11.2213302,11 10.92917,11.2124899 10.8417969,11.4746094 L10.6582031,12.0253906 C10.5695229,12.2914314 10.42917,12.2875101 10.3417969,12.0253906 L10.1582031,11.4746094 C10.0695229,11.2085686 9.77263606,11 9.49216843,11 L9.00783157,11 C8.72133017,11 8.62234016,10.8164898 8.77325439,10.5901184 L9.22674561,9.90988159 C9.37738037,9.68392944 9.28324125,9.55418969 9.01585579,9.62103605 L7.98414421,9.87896395 C7.71403503,9.94649124 7.27614237,10 7,10 C6.73193359,10 6.68120372,9.86409721 6.90472984,9.69645262 L8.09527016,8.80354738 C8.31542969,8.63842773 8.28418999,8.456838 8.01797485,8.40359497 L6.48202515,8.09640503 C6.21314859,8.04262972 6.18351024,7.87765984 6.40988159,7.72674561 L7.09011841,7.27325439 C7.31607056,7.12261963 7.30127495,6.90063747 7.05613518,6.77806759 L5.94386482,6.22193241 C5.69907951,6.09953976 5.65658907,5.84341093 5.84975147,5.65024853 L6.15024853,5.34975147 C6.34375,5.15625 6.69872505,5.09936253 6.94386482,5.22193241 L8.05613518,5.77806759 C8.30092049,5.90046024 8.72385763,6 9,6 C9.26806641,6 9.5,5.77614237 9.5,5.5 C9.5,5.23193359 9.30127495,4.90063747 9.05613518,4.77806759 L7.94386482,4.22193241 C7.69907951,4.09953976 7.68351024,3.87765984 7.90988159,3.72674561 L8.59011841,3.27325439 C8.81607056,3.12261963 9.15576031,3.15576031 9.34790039,3.34790039 Z" id="Combined-Shape" fill="url(#linearGradient-3)"></path>
        </g>
    </g>
</svg>