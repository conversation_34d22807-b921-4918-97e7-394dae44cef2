{"name": "wizai-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "3.777.0", "@aws-sdk/s3-request-presigner": "^3.777.0", "@codemirror/lang-javascript": "^6.2.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@headlessui/react": "^1.7.8", "@hookform/resolvers": "^3.3.1", "@langchain/community": "^0.3.35", "@langchain/core": "^0.3.29", "@langchain/openai": "^0.4.4", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@next/swc-wasm-nodejs": "13.5.1", "@pinecone-database/pinecone": "^4.0.0", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-oauth/google": "^0.11.1", "@reduxjs/toolkit": "^1.9.1", "@shikijs/transformers": "^3.2.1", "@stripe/react-stripe-js": "^2.3.0", "@stripe/stripe-js": "^2.1.5", "@tanstack/react-query": "^4.28.0", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tippyjs/react": "^4.2.6", "@types/node": "20.9.0", "@types/prismjs": "^1.26.3", "@types/react": "18.0.27", "@types/react-color": "^3.0.12", "@types/react-dom": "18.0.10", "@types/uuid": "^10.0.0", "@uiw/codemirror-theme-vscode": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "@vercel/blob": "^0.26.0", "apexcharts": "^3.37.1", "axios": "^1.3.4", "braintree-web-drop-in-react": "^1.2.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.32.0", "eslint-config-next": "13.1.2", "formidable": "^3.5.2", "highlight.js": "^11.7.0", "html2canvas": "^1.4.1", "i18next": "^22.4.10", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "langchain": "^0.3.19", "lucide-react": "^0.446.0", "mammoth": "^1.9.0", "mantine-datatable": "^1.7.35", "moment": "^2.29.4", "mongodb": "^6.14.2", "next": "13.1.2", "next-themes": "^0.3.0", "ni18n": "^1.0.5", "node-poppler": "^7.2.4", "nprogress": "^0.2.0", "openai": "^4.71.1", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "prismjs": "^1.30.0", "react": "18.2.0", "react-animate-height": "^3.1.0", "react-apexcharts": "^1.4.0", "react-audio-voice-recorder": "^2.2.0", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-data-grid": "^7.0.0-beta.40", "react-datepicker": "^4.18.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-ga": "^3.3.1", "react-hook-form": "7.25.0", "react-hot-toast": "^2.5.2", "react-i18next": "^12.1.5", "react-icons": "^4.11.0", "react-input-color": "^4.0.1", "react-paginate": "^8.2.0", "react-perfect-scrollbar": "^1.5.8", "react-popper": "^2.3.0", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-resizable-panels": "^2.1.3", "react-select": "^5.7.0", "react-table": "^7.8.0", "react-to-pdf": "^2.0.0", "react-to-print": "^3.0.2", "react-toastify": "^9.1.2", "recharts": "^2.12.7", "shiki": "^3.2.1", "sonner": "^1.5.0", "sortablejs": "^1.15.0", "stripe": "^17.3.1", "sweetalert2": "^11.7.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.0", "typescript": "4.9.4", "uuid": "^11.1.0", "vaul": "^0.9.9", "word-extractor": "^1.0.4", "yarn": "^1.22.19", "yup": "^0.32.11", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/braintree-web-drop-in": "^1.39.1", "@types/formidable": "^3.4.5", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.191", "@types/node": "20.9.0", "@types/nprogress": "^0.2.1", "@types/react-datepicker": "^4.15.1", "@types/react-table": "^7.7.16", "autoprefixer": "10.4.15", "eslint-config-prettier": "^9.1.0", "postcss": "8.4.30", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "3.3.3"}}