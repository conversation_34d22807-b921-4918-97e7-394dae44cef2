import { Body, Controller, Delete, Get, Param, Post, Put } from '@nestjs/common';
import { ResumeService } from './resume.service';
import { CreateResumeDto, ResumeIdParamDto, UpdateResumeDto } from './dto/resume.dto';

@Controller('resume-builder/resumes')
export class ResumeController {
  constructor(private readonly resumeService: ResumeService) {}

  @Get()
  async getAllResumes() {
    return this.resumeService.getAllResumes();
  }

  @Get(':id')
  async getResumeById(@Param() params: ResumeIdParamDto) {
    return this.resumeService.getResumeById(params.id);
  }

  @Post()
  async createResume(@Body() createResumeDto: CreateResumeDto) {
    return this.resumeService.createResume(createResumeDto);
  }

  @Put(':id')
  async updateResume(
    @Param() params: ResumeIdParamDto,
    @Body() updateResumeDto: UpdateResumeDto,
  ) {
    return this.resumeService.updateResume(params.id, updateResumeDto);
  }

  @Delete(':id')
  async deleteResume(@Param() params: ResumeIdParamDto) {
    await this.resumeService.deleteResume(params.id);
    return { success: true };
  }
} 