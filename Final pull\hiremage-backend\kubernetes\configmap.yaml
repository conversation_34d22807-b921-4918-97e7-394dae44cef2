apiVersion: v1
kind: ConfigMap
metadata:
  name: hiremage-backend-config
data:
  # Database connection (non-sensitive parts)
  DB_NAME: "hiremage"
  DB_USERNAME: "admin"
  DB_HOST: "hiremage-backend-prod.cxouigisol0l.ap-south-1.rds.amazonaws.com"
  DB_PORT: "3306"
  
  # Application settings
  PORT: "3000"
  FRONTEND_URL: "http://localhost:3001"
  BACKEND_URL: "http://localhost:3000"
  
  # AWS S3 Configuration (non-sensitive parts)
  AWS_REGION: "ap-south-1"
  AWS_S3_BUCKET_NAME: "hiremage-backend"
  AWS_S3_URL_PREFIX: "https://hiremage-backend.s3.amazonaws.com"
  
  # Other configurations
  JWT_SIGNATURE: "theSuperSecretKey"
  API_SECRET: "fdjsbfkjdsfjkdskjf"
  ACCESS_TOKEN_EXPIRY: "50d"
  # Add other non-sensitive environment variables 