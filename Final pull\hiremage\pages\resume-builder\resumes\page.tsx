"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusSquare } from "lucide-react";
import Link from "next/link";
import ResumeItem from "./ResumeItem";

export default function Page() {
  // Mock implementation - in a real app, you would fetch from your database here
  const resumes = [
    {
      id: "1",
      title: "Software Engineer Resume",
      description: "Professional resume for software engineering positions",
      firstName: "<PERSON>",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "****** 567 8900",
      location: "San Francisco, CA",
      summary: "Experienced software engineer with 5+ years of experience",
      workExperiences: [],
      educations: [],
    },
    {
      id: "2",
      title: "Product Manager Resume",
      description: "Resume for product management roles",
      firstName: "Jane",
      lastName: "Smith",
      email: "<EMAIL>",
      phone: "****** 567 8901",
      location: "New York, NY",
      summary: "Product manager with expertise in agile methodologies",
      workExperiences: [],
      educations: [],
    },
  ];
  const totalCount = resumes.length;

  return (
    <main className="mx-auto w-full max-w-7xl space-y-6 px-3 py-6">
      <Button asChild className="mx-auto flex w-fit gap-2">
        <Link href="/editor">
          <PlusSquare className="size-5" />
          New resume
        </Link>
      </Button>
      <div className="space-y-1">
        <h1 className="text-3xl font-bold">Your resumes</h1>
        <p>Total: {totalCount}</p>
      </div>
      <div className="flex w-full grid-cols-2 flex-col gap-3 sm:grid md:grid-cols-3 lg:grid-cols-4">
        {resumes.map((resume) => (
          <ResumeItem key={resume.id} resume={resume} />
        ))}
      </div>
    </main>
  );
}
