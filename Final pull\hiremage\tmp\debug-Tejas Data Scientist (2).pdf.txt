  
➢ Data Science professional with rich and qualitative experience of 5 years in Healthcare Industry, 
Banking application enhancement, Retail, Auto Construction, Mobile Industry.  
➢ Expert in defining business problem, worked on manipulating data, developing analytical algorithms 
and solutions, devising analytical models into production environment.  
➢ Hands on experience in Deep Learning Technologies to develop prototypes and analytical models with 
new and existing clients using the application of new, innovative capabilities.  
➢ Worked closely with various teams to encourage statistical and experimental design, data capture and 
data analysis.  
➢ Qualitative experience in leveraging Machine Learning Algorithms and applied statistics with 
visualization and strong sense of data exploration. 
➢ Experience in leading, building, and improving Machine learning projects and understanding of 
enterprise level Machine learning use cases. 
➢ Knowledge of machine learning frameworks such as Scikit-Learn, Tensorflow, Keras or PyTorch and has 
experience with cloud platform such as AWS. 
➢ Expertise in gathering data from various sources, including databases, APIs and third-party tools. 
➢ Proficient in cleaning and pre-processing data to ensure accuracy and consistency. 
➢ Ability to identify trends, patterns and anomalies in data to inform business decisions.  
➢ Experience in applying statistical tools to solve business problems. 
➢ Knowledge of statistical techniques and methods such as regression analysis, hypothesis testing and 
correlation analysis. 
➢ Skilled in presenting data findings through interactive dashboard using business intelligence tools.  
➢ Experience in communicating insights and recommendations to stakeholders through written, 
visualization and verbal presentations. 
➢ Strong SQL skills for querying and managing relational databases. Familiarity with databases design 
and management principles. 
➢ Proficient in Computer Vision, AWS, Azure and programming languages such as Python. 
➢ Experience of working on complex projects and deliver data driven solutions under tight deadlines. 
➢ Experience in managing multiple projects simultaneously and prioritizing tasks effectively. 
➢ Excellent skills to work effectively with cross-functional teams, product managers and business analysts 
to explain technical aspects to non-technical stakeholders.  
➢ Ability to work independently and take ownership of data analysis projects from start to finish.  
➢ Understanding of the business context and ability to align data analysis with business objectives. 
Knowledge of industry specific metrics and KPIs relevant to the role. 
➢ Commitment to staying updated with latest trends, tools and best practices in data analysis and to 
acquire new skills and adapt to changing technologies 
TEJAS B N 
  
Data Scientist 
Engineer
Address  Bangalore ,  Karanataka   
Phone     7892061189 
E-mail  <EMAIL> 
  
Skills 
 
  Programming: Python  
Very Good 
 Libraries: NumPy, Pandas, SciPy, Scikit-Learn, Matplotlib   
Very Good 
 Business Intelligence tools: Power BI, Tableau   
Very Good 
 Databases: SQL    
Very Good 
                                              Mathematical Skills: Statistics                                               
Very Good 
                                   ML, DL and Cloud(AWS, Azure)                                                      
Very Good 
Work History 
 
2018 – 2020             Catalog Analyst 
                             Amazon 
2020-2022                   Associate Data Scientist 
                                     NEXXOFT infotel 
2022-Current    Data Scientist 
                                     NEXXOFT infotel 
 
Education 
 
2014 – 2016                    Master of Technology 
REVA, Bengaluru 
2023 -Current      PhD (Machine Leaning Techniques for Open Set Classification of Audio Signals) 
        M S Ramaiah University of Applied Sciences, Bengaluru 
 
Projects 
 
Project 1: Personalized Travel Recommendations and Target Marketing Campaigns through 
Generative AI Analysis 
This   innovative   project   leverages   cutting-edge   generative   AI   to   create   personalized   travel 
recommendations, flight descriptions, and targeted marketing campaigns. By analyzing extensive user 
data, including preferences, behaviors, and past  interactions, the system tailors   content  to  individual 
preferences, thereby significantly enhancing customer engagement and boosting conversion rates. This 
approach  not  only  provides  customers  with  highly  relevant and  appealing  content  but  also  enables 
businesses  to  maximize  their  marketing  impact  and drive  revenue growth through more effective 
campaigns. 
 
 
Skills:                                                                                   Tools and Techniques: 
 Prompt engineering      Large Language Models (LLMs) 
 LLM application development                                                    Generative AI 
 Fine Tuning LLMs 
 Using semantic search 
 RAG 
 
Responsibility: 
 Developed and implemented generative AI models for     personalized travel recommendations and 
targeted marketing campaigns. 
 Analysed user data to extract meaningful insights and    patterns for customization. 
 Deployed and maintained machine learning models for real-time recommendations. 
 Built API s and back-end systems to support AI model      integration and data processing. 
 Designed data pipelines for collecting, storing, and processing large volumes of user data for AI analysis. 
 Stayed abreast of the latest advancements in generative AI, machine learning, and data processing 
techniques, integrating new methodologies and technologies to enhance the capabilities and accuracy 
of the recommendation system. 
 
 
Project 2: Social Sentiment Analysis: Monitoring brand Perception on Social Media 
This project involves monitoring social media channels to track conversations about the retailer's brand, 
products,  and  competitors.  Using  deep  learning  algorithms  like  RNNs, LSTMs,  and  Transformers  for 
sentiment  analysis,  retailers  can  gauge  public  perception, identify trends, and respond to customer 
queries or complaints in a timely manner, thereby managing their brand reputation effectively. 
 
  Algorithms:                  Tools and Techniques: 
 RNN                  Python, Numpy, Pandas, TensorFlow 
 LSTM            Scikit Learn, Matplotlib, Keras   
             
Responsibilities: 
 Develop and implement deep learning models, including RNNs, LSTMs, and Transformers, for sentiment 
analysis to accurately gauge public perception and sentiment towards the retailer's brand.
 Collaborate  with  cross-functional  teams  to  integrate sentiment  analysis  models into  a  real-time 
monitoring system, enabling timely responses to customer queries, complaints, and emerging trends.
 Continuously evaluate and optimize model performance by fine-tuning algorithms, experimenting with 
new techniques, and incorporating feedback from on-going monitoring efforts.
 Provide  actionable  insights  and  recommendations  to stakeholders  based  on  the  analysis  of  social 
sentiment data, enabling informed decision-making and proactive brand management strategies.


Project 3: Cashier less Stores Using Computer Vision and Deep Learning 
Cashierless stores leverage computer vision and deep learning technologies to enable self-checkout. 
These stores automatically detect product prices and calculate bills as shoppers select items, improving 
efficiency and convenience while reducing checkout times. 
      Algorithms:                 Tools and Techniques: 
 YOLO v8        Python, NumPy, Pandas 
 YOLO v7        Scikit-learn, Matplotlib 
            CNN, Computer- Vision 
Responsibilities: 
 Pre-processing and analyzing store image/video data to ensure accuracy and relevance for model training. 
 Evaluating the performance of YOLO models (v7 and v8) through comprehensive metrics assessment. 
 Collaborating closely with the Deep Learning Engineer to extract pertinent features from the data for model 
enhancement. 
 Providing actionable insights derived from data analysis to optimize object detection algorithms and refine the 
cashier-less store system. 
 Continuously iterating and refining data-driven strategies to enhance the overall efficiency and effectiveness 
of the cashier-less shopping experience 
 
 
Project 4: Predicting Customer Churn Retention Strategies for Retailers 
This project aims to predict whether a customer is likely to churn (leave) or not based on their behavior, interactions, 
and transaction history. By using algorithms like Logistic Regression, Random Forest, and Gradient Boosting, retailers 
can proactively identify at-risk customers and implement targeted retention strategies to reduce churn rates. 
Algorithms:             Tools and Techniques: 
 Logistic Regression       Python, Numpy 
 Decision Trees           Pandas, Scikit-Learn 
 Random Forest        Matplotlib        
 Gradient Boosting             
Responsibilities: 
 Created data pipelines to collect, clean, and pre-process customer interaction data.
 Analyzed churn predictions to develop targeted retention strategies.
 Integrated Predictive models into existing systems for real time churn prediction.
 Implemented and monitored retention strategies based on churn predictions to improve customer 
retention rates.
 Conducted feature  engineering to  identify  key  behavioural indicators and transaction patterns that 
correlate with customer churn, enhancing the predictive power of the models.
 Collaborated  with  cross-functional  teams  including  marketing, sales,  and  customer  service  to  ensure 
alignment of retention strategies with overall business objectives and customer experience goals.
 Utilized A/B  testing  methodologies to  evaluate  the  effectiveness of  different  retention  strategies, 
iterating  and  refining  approaches based  on  empirical  results  to  continually  optimize  customer 
retention efforts.


Date:           Thanks, and Regards, 
Place: Bengaluru                   Tejas B N 