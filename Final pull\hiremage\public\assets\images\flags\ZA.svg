<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>ZA</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#06A86E" offset="0%"></stop>
            <stop stop-color="#007A4E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFBF2E" offset="0%"></stop>
            <stop stop-color="#FFB612" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#0D0D0D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#F44E46" offset="0%"></stop>
            <stop stop-color="#DF3931" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#072CB4" offset="0%"></stop>
            <stop stop-color="#042396" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="ZA">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <polygon id="Rectangle-83" fill="url(#linearGradient-3)" points="0 2 7 7.5 0 13"></polygon>
            <polygon id="Rectangle-83-Copy-5" fill="url(#linearGradient-4)" points="-1 2.25 5.75 7.5 -1 12.75"></polygon>
            <polygon id="Rectangle-83-Copy" fill="#FFFFFF" points="9 6 2 0 21 0 21 6"></polygon>
            <polygon id="Rectangle-83-Copy-3" fill="url(#linearGradient-5)" points="9.25 5 3.25 0 21 0 21 5"></polygon>
            <polygon id="Rectangle-83-Copy-2" fill="#FFFFFF" points="2 15 21 15 21 9 9 9"></polygon>
            <polygon id="Rectangle-83-Copy-4" fill="url(#linearGradient-6)" points="3.25 15 21 15 21 10 9.25 10"></polygon>
        </g>
    </g>
</svg>