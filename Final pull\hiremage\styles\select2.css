.custom-select .css-1dimb5e-singleValue,
.custom-select .css-166bipr-Input,
.custom-select .css-qbdosj-Input {
  @apply text-black dark:text-white-dark;
}

.custom-select .css-1fdsijx-ValueContainer {
  @apply !pl-4;
}
.custom-select .css-b62m3t-container > div {
  @apply border-[rgb(224,230,237)] bg-white hover:border-inherit dark:border-[#253b5c] dark:bg-[#1b2e4b];
}
.custom-select .css-b62m3t-container > div:first-of-type {
  @apply shadow-none;
}

.custom-select .css-1nmdiq5-menu > div > div {
  @apply !bg-transparent hover:!bg-[#f6f6f6] dark:hover:!bg-[#132136];
}
.custom-select .css-1nmdiq5-menu > div > div[aria-disabled="true"] {
  @apply !bg-transparent !text-[#999];
}
.custom-select .css-1nmdiq5-menu > div > .css-tr4s17-option {
  @apply !bg-[#f6f6f6] !text-black dark:!bg-[#132136] dark:!text-white-dark;
}
.custom-select .css-1xc3v61-indicatorContainer {
  @apply text-[#999] dark:text-white-dark;
}
.custom-select .css-1u9des2-indicatorSeparator {
  @apply hidden;
}
.custom-select .css-1p3m7a8-multiValue {
  @apply !bg-success !text-white;
}
.custom-select .css-wsp0cs-MultiValueGeneric {
  @apply !text-white;
}
.wizai-select__control {
  @apply !rounded-md !border-white-light dark:!border-[#17263c] dark:bg-[#121e32];
}
.wizai-select__value-container {
  @apply !text-sm;
}
.wizai-select__indicator-separator {
  @apply !w-0;
}

.wizai-select__menu {
  @apply dark:!border-[#17263c] dark:bg-[#121e32];
}
.wizai-select__option {
  @apply !cursor-pointer dark:hover:!text-dark;
}
.wizai-select__option--is-focused {
  @apply dark:!text-dark;
}
.wizai-select__single-value {
  @apply dark:!text-white;
}

.wizai-select__multi-value {
  @apply dark:!bg-dark dark:!text-white;
}

.wizai-select__multi-value__label {
  @apply dark:!text-white;
}

.wizai-select__multi-value__remove {
  @apply !cursor-pointer dark:hover:!bg-white dark:hover:!text-dark;
}
