# Simplified Kubernetes Deployment Guide

## Steps to Deploy and Access Your App

1. **Build your Docker image**
   ```bash
   docker build -t hiremage-backend:latest .
   ```

2. **Apply Kubernetes manifests**
   ```bash
   kubectl apply -k kubernetes/
   ```

3. **Check that everything is running**
   ```bash
   kubectl get pods
   kubectl get svc
   ```

4. **Start the application with port-forward script**
   
   For Windows:
   ```
   start-hiremage.bat
   ```
   
   For Mac/Linux:
   ```
   chmod +x start-hiremage.sh
   ./start-hiremage.sh
   ```

5. **Access your app at**
   http://localhost:3000

## Environment Variables

The application uses environment variables from Kubernetes ConfigMaps and Secrets instead of a .env file:

1. **ConfigMap (non-sensitive data)** 
   - Located at `kubernetes/configmap.yaml`
   - Contains database connection info, URLs, and other settings
   - Edit this file to change application settings

2. **Secret (sensitive data)**
   - Located at `kubernetes/secret.yaml`
   - Contains passwords, access keys, and tokens
   - Values are base64 encoded
   - To add a new secret: `echo -n "your-secret" | base64`

3. **Modifying environment variables**
   - Edit the appropriate file (configmap.yaml or secret.yaml)
   - Apply changes: `kubectl apply -f kubernetes/configmap.yaml`
   - Restart the deployment: `kubectl rollout restart deployment hiremage-backend`

## Troubleshooting

1. **If pod is not running**
   ```bash
   kubectl describe pod [pod-name]
   kubectl logs [pod-name]
   ```

2. **Delete and recreate everything**
   ```bash
   kubectl delete -k kubernetes/
   kubectl apply -k kubernetes/
   ```

3. **Check Docker image exists**
   ```bash
   docker images | grep hiremage-backend
   ```

4. **Verify pod can access ports**
   ```bash
   kubectl exec -it [pod-name] -- curl localhost:3000
   ```

5. **Alternative direct port-forward**
   ```bash
   # Run this command and leave the terminal open
   kubectl port-forward pods/[pod-name] 3000:3000
   ```

## Common Issues and Fixes

- **Image pull errors**: Make sure your image is built locally and imagePullPolicy is set to Never

- **Port already in use**: Kill any existing kubectl port-forward processes and try again

- **Application errors**: Check pod logs for application-specific errors 