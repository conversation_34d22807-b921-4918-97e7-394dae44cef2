apiVersion: apps/v1
kind: Deployment
metadata:
  name: hiremage-backend
  labels:
    app: hiremage-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hiremage-backend
  template:
    metadata:
      labels:
        app: hiremage-backend
    spec:
      containers:
      - name: hiremage-backend
        image: hiremage-backend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 3000
          name: http
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        env:
        - name: NODE_ENV
          value: "production"
        
        # Load environment variables from ConfigMap
        envFrom:
        - configMapRef:
            name: hiremage-backend-config
        
        # Load sensitive environment variables from Secret
        - secretRef:
            name: hiremage-backend-secrets
        
        # Add DATABASE_URL environment variable that depends on other env vars
        env:
        - name: DATABASE_URL
          value: "mysql://$(DB_USERNAME):$(DB_PASSWORD)@$(DB_HOST):$(DB_PORT)/$(DB_NAME)"
        
        # Add health checks
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 10