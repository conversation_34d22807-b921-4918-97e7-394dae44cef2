import type { NextApiRequest, NextApiResponse } from 'next';
import { MongoClient } from 'mongodb';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { randomUUID } from 'crypto';
import formidable from 'formidable';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';

// Disable the default body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Get S3 configuration
  const region = process.env.AWS_REGION || 'ap-south-1';
  const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
  const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
  const bucketName = process.env.AWS_S3_BUCKET || 'hiremage-backend';

  if (!accessKeyId || !secretAccessKey || !bucketName) {
    return res.status(500).json({ error: 'Server configuration error' });
  }

  const mongoClient = new MongoClient(process.env.MONGODB_URI ?? '');
  let filePath: string | undefined;
  
  try {
    // Initialize S3 client
    const s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    // Parse form data
    const form = formidable({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });
    
    const [fields, files] = await new Promise<[any, any]>((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) return reject(err);
        resolve([fields, files]);
      });
    });

    // Get file object
    const fileObj = files.file;
    if (!fileObj) {
      return res.status(400).json({ error: 'No file provided' });
    }
    
    // Handle both array and single file formats
    const file = Array.isArray(fileObj) ? fileObj[0] : fileObj;
    
    // Get filepath - handle both formidable v3 and v4 APIs
    const filepath = file.filepath || file.path;
    if (!filepath) {
      return res.status(500).json({ error: 'File path not found in uploaded file' });
    }

    // Get file details
    const originalFilename = file.originalFilename || file.name || 'unnamed';
    const mimetype = file.mimetype || file.type || 'application/octet-stream';
    const userId = (req.headers['x-user-id'] as string) || 'default-user';
    
    // Generate a unique resumeId
    const resumeId = randomUUID();
    
    // Read file content
    const fileContent = await fs.readFile(filepath);
    
    // Generate a unique S3 key (file path in S3)
    const s3Key = `resumes/${userId}/${resumeId}/${originalFilename}`;
    
    console.log(`Uploading to S3 with key: ${s3Key}`);

    // Upload to S3
    await s3Client.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: s3Key,
      Body: fileContent,
      ContentType: mimetype,
      // ACL: 'public-read', // Remove - let backend use its credentials
    }));

    // Connect to MongoDB
    await mongoClient.connect();
    const db = mongoClient.db('resume-metadata');
    const collection = db.collection('resumes');
    
    // Store metadata in MongoDB
    await collection.insertOne({
      resumeId,
      userId,
      fileName: originalFilename,
      s3Key,
      s3Bucket: bucketName,
      uploadDate: new Date().toISOString(),
      processed: false,
      metadata: {
        fileName: originalFilename,
        userId,
        uploadDate: new Date().toISOString(),
        resumeId,
      }
    });

    // Clean up temp file
    try {
      await fs.unlink(filepath);
    } catch (unlinkError) {
      // Continue anyway if cleanup fails
      console.error('Failed to clean up temp file:', unlinkError);
    }

    // Return success with S3 URL and resumeId
    const s3Url = `https://${bucketName}.s3.${region}.amazonaws.com/${s3Key}`;
    
    return res.status(200).json({
      message: 'Resume uploaded successfully to S3',
      resumeId,
      fileName: originalFilename,
      s3Url,
    });

  } catch (err: any) {
    console.error('Error uploading resume to S3:', err);
    return res.status(500).json({ error: err.message });
  } finally {
    await mongoClient.close();
  }
}