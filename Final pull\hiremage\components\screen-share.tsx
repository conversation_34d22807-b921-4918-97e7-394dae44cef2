'use client';

import { useEffect, useRef } from 'react';
import { useScreenShare } from '@/hooks/use-screen-share';
import { useMediaPermissions } from '@/hooks/use-media-permissions';
import { Button } from './ui/button';
import { Monitor, StopCircle } from 'lucide-react';

export default function ScreenShare() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { stream, error, startScreenShare, stopScreenShare } = useScreenShare();
  const { permissions, requestPermission } = useMediaPermissions();

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);

  const handleStartSharing = async () => {
    const granted = await requestPermission('screen');
    if (granted) {
      startScreenShare();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Screen Share</h3>
        {stream ? (
          <Button variant="destructive" onClick={stopScreenShare}>
            <StopCircle className="mr-2 h-4 w-4" />
            Stop Sharing
          </Button>
        ) : (
          <Button onClick={handleStartSharing}>
            <Monitor className="mr-2 h-4 w-4" />
            Share Screen
          </Button>
        )}
      </div>
      
      {error && (
        <p className="text-destructive text-sm bg-destructive/10 p-2 rounded">
          {error.message}
        </p>
      )}
      
      {stream && (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          className="w-full aspect-video rounded-lg bg-secondary"
        />
      )}
    </div>
  );
}