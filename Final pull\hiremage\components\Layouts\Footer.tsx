import { IRootState } from "@/store";
import { useSelector } from "react-redux";
import Link from "next/link";
import { useTranslation } from "react-i18next";

const Footer = () => {
  const { t } = useTranslation();
  const { settings } = useSelector((state: IRootState) => state?.common?.data);
  
  // Check if we're in interview mode
  const isInterviewMode = typeof document !== 'undefined' && 
    document.body.classList.contains('full-page-interview');
  
  // Don't render anything in interview mode
  if (isInterviewMode) {
    return null;
  }
  
  return (
    <footer className="minimal-footer bg-white dark:bg-black dark:text-white">
      <div className="px-5 md:px-10">
        <div className="mx-auto w-full max-w-7xl">
          <div className="py-4">
            <div className="flex flex-col items-center">
              <p className="text-sm">
                © {new Date().getFullYear()} {settings?.site_copy_right_text || t('All Rights Reserved')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
