'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import InterviewSetup from '@/components/interview-setup';
import { Card } from '@/components/ui/card';
import { FileText, Trash2 } from 'lucide-react';
import { toast } from 'react-toastify';

interface Resume {
  id: string;
  fileName: string;
  uploadDate: string;
  resumeId: string;
}

export default function Home() {
  const [isSetupOpen, setIsSetupOpen] = useState(false);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch resumes on component mount
  useEffect(() => {
    fetchResumes();
  }, []);

  const fetchResumes = async () => {
    try {
      const response = await fetch('/api/getResumes', {
        headers: {
          'x-user-id': 'default-user', // Replace with actual user ID from auth
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch resumes');
      }

      const data = await response.json();
      setResumes(data);
    } catch (error) {
      console.error('Error fetching resumes:', error);
      toast.error('Failed to load resumes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartSession = () => {
    setSessionStarted(true);
    setIsSetupOpen(false);
    fetchResumes(); // Refresh the list after upload
  };

  const handleDeleteResume = async (id: string, resumeId: string) => {
    try {
      const response = await fetch(`/api/deleteResume?resumeId=${encodeURIComponent(resumeId)}`, {
        method: 'DELETE',
        headers: {
          'x-user-id': 'default-user', // Replace with actual user ID from auth
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete resume');
      }

      // Only update UI if deletion was successful
      setResumes(resumes.filter(resume => resume.id !== id));
      toast.success('Resume deleted successfully');
    } catch (error: any) {
      console.error('Error deleting resume:', error);
      toast.error(error.message || 'Failed to delete resume');
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-secondary">
      <div className="container mx-auto px-4 py-8">
        {/* Header with Create Resume button */}
        <div className="flex justify-between items-center mb-8">
          <Button size="lg" onClick={() => setIsSetupOpen(true)}>
            Upload Resume
          </Button>
        </div>

        {/* Resumes List */}
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-semibold mb-6">Your Resumes</h2>
          <div className="grid gap-4">
            {isLoading ? (
              <Card className="p-6 text-center text-muted-foreground">
                Loading resumes...
              </Card>
            ) : resumes.length === 0 ? (
              <Card className="p-6 text-center text-muted-foreground">
                No resumes uploaded yet. Click "Upload Resume" to get started.
              </Card>
            ) : (
              resumes.map((resume) => (
                <Card key={resume.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <FileText className="w-6 h-6 text-primary" />
                      <div>
                        <h3 className="font-medium">{resume.fileName}</h3>
                        <p className="text-sm text-muted-foreground">
                          Uploaded on {new Date(resume.uploadDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteResume(resume.id, resume.resumeId)}
                      className="text-red-500 hover:text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="w-5 h-5" />
                    </Button>
                  </div>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>

      <InterviewSetup
        open={isSetupOpen}
        onClose={() => setIsSetupOpen(false)}
        onStart={handleStartSession}
      />
    </main>
  );
}