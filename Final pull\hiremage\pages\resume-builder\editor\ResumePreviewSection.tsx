"use client";

import ResumePreview from "@/components/ResumePreview";
import { cn } from "@/lib/utils";
import { ResumeValues } from "@/lib/validation";
import BorderStyleButton from "./BorderStyleButton";
import ColorPicker from "./ColorPicker";
import FontPicker from "./FontPicker";

interface ResumePreviewSectionProps {
  resumeData: ResumeValues;
  setResumeData: (data: ResumeValues) => void;
  className?: string;
}

export default function ResumePreviewSection({
  resumeData = {} as ResumeValues,
  setResumeData = () => {},
  className,
}: ResumePreviewSectionProps) {
  // For SSR - return minimal markup
  if (typeof window === 'undefined') {
    return (
      <div className={cn("group relative flex flex-col border-l border-gray-200 bg-white", className)}>
        <div className="flex flex-1 w-full items-center justify-center overflow-y-auto p-6">
          <div className="h-auto w-full max-w-md shadow-md lg:max-w-lg" />
        </div>
      </div>
    );
  }
  
  return (
    <div
      className={cn("group relative flex flex-col border-l border-gray-200 bg-white", className)}
    >
      <div className="absolute bottom-4 left-4 z-10 flex flex-none flex-row gap-3 bg-background/80 p-2 rounded-lg shadow-md backdrop-blur-sm">
        <div className="flex flex-col gap-1.5 items-center">
          <span className="text-xs font-medium">Color</span>
          <ColorPicker
            color={resumeData?.colorHex}
            onChange={(color) =>
              setResumeData({ ...resumeData, colorHex: color.hex })
            }
          />
        </div>
        <div className="flex flex-col gap-1.5 items-center">
          <span className="text-xs font-medium">Font</span>
          <FontPicker
            font={resumeData?.fontFamily}
            onChange={(fontFamily) =>
              setResumeData({ ...resumeData, fontFamily })
            }
          />
        </div>
        <div className="flex flex-col gap-1.5 items-center">
          <span className="text-xs font-medium">Border</span>
          <BorderStyleButton
            borderStyle={resumeData?.borderStyle}
            onChange={(borderStyle) =>
              setResumeData({ ...resumeData, borderStyle })
            }
          />
        </div>
      </div>
      <div className="flex flex-1 w-full items-center justify-center overflow-y-auto p-6">
        <ResumePreview
          resumeData={resumeData}
          className="h-auto w-full max-w-md shadow-md lg:max-w-lg"
        />
      </div>
    </div>
  );
}
