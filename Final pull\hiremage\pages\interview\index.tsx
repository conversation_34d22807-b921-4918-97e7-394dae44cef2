import { useTranslation } from "react-i18next";
import { useState, FormEvent, useEffect, useRef } from "react";
import { FaMicrophone, FaStop, FaFileAlt, FaUserTie, FaBrain, FaList, FaFileContract, FaPen } from "react-icons/fa";
import { IoMdArrowBack } from "react-icons/io";
import { useDispatch, useSelector } from "react-redux";
import { toggleSidebar } from "../../store/themeConfigSlice";
import dynamic from "next/dynamic";
import { toast } from "react-toastify";
import { useSubscriptionStatus } from "@/hooks/paymentSettings.hook";
import { useRouter } from "next/router";
import Swal from "sweetalert2";
import SearchableSelect from "@/components/SearchableSelect";

// Interface for Resume objects
interface Resume {
  id: string;
  fileName: string;
  uploadDate: string;
  role: string;
  mode: string;
  resumeId: string;
}

// Dynamically import the interview session component to avoid SSR issues
const InterviewSession = dynamic(() => import("../../components/interview-session"), {
  ssr: false,
});

export default function LiveInterview() {
  const { t } = useTranslation();
  const router = useRouter();
  const [showForm, setShowForm] = useState(false);
  const [isSessionActive, setIsSessionActive] = useState(false);
  const dispatch = useDispatch();
  const { data: subscriptionData, isLoading: isSubscriptionLoading } = useSubscriptionStatus();
  const [previousInterviews, setPreviousInterviews] = useState([
    { id: 1, title: "Frontend Developer Interview", date: "2023-05-15", duration: "25 min" },
    { id: 2, title: "Product Manager Interview", date: "2023-05-10", duration: "32 min" },
    { id: 3, title: "Data Scientist Interview", date: "2023-05-02", duration: "28 min" },
  ]);
  
  // Form state
  const [selectedResume, setSelectedResume] = useState("");
  const [selectedResumeId, setSelectedResumeId] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [customRole, setCustomRole] = useState("");
  const [jobDescription, setJobDescription] = useState("");
  const [selectedInterviewType, setSelectedInterviewType] = useState("");
  
  // State for user's resumes
  const [userResumes, setUserResumes] = useState<Resume[]>([]);
  const [isLoadingResumes, setIsLoadingResumes] = useState(false);

  // Fetch user's resumes from Resume Hub
  useEffect(() => {
    if (showForm) {
      fetchUserResumes();
    }
  }, [showForm]);

  // Function to fetch user's resumes
  const fetchUserResumes = async () => {
    setIsLoadingResumes(true);
    try {
      const response = await fetch('/api/getResumes', {
        headers: {
          'x-user-id': 'default-user', // Replace with actual user ID from auth when implemented
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch resumes');
      }

      const data = await response.json();
      setUserResumes(data);
      
      // If there are resumes and none selected yet, select the first one
      if (data.length > 0 && !selectedResume) {
        setSelectedResume(data[0].id);
        setSelectedResumeId(data[0].resumeId);
      }
    } catch (error) {
      console.error('Error fetching resumes:', error);
      toast.error('Failed to load resumes. Please try again.');
    } finally {
      setIsLoadingResumes(false);
    }
  };

  const toggleForm = () => {
    setShowForm(!showForm);
    // When opening the form, fetch resumes
    if (!showForm) {
      fetchUserResumes();
    }
  };

  const handleResumeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newResumeId = e.target.value;
    setSelectedResume(newResumeId);
    
    // Find the corresponding resumeId
    const selectedResume = userResumes.find(resume => resume.id === newResumeId);
    if (selectedResume) {
      setSelectedResumeId(selectedResume.resumeId);
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Check if there's a selected resume
    if (!selectedResume) {
      toast.error('Please select a resume for the interview');
      return;
    }
    
    // Check if there's a selected role
    if (!selectedRole) {
      toast.error('Please select a role for the interview');
      return;
    }

    // Check if "Other" is selected but no custom role is entered
    if (selectedRole === "other" && !customRole.trim()) {
      toast.error('Please enter a custom role');
      return;
    }

    // Check if there's a job description
    if (!jobDescription.trim()) {
      toast.error('Please enter a job description for the interview');
      return;
    }

    // Check subscription status
    if (!subscriptionData?.success) {
      const result = await Swal.fire({
        title: 'Subscription Required',
        text: 'You need to subscribe to access the interview feature. Would you like to subscribe now?',
        icon: 'info',
        showCancelButton: true,
        confirmButtonText: 'Subscribe',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
      });

      if (result.isConfirmed) {
        router.push('/upgrade');
        return;
      }
      return;
    }
    
    // Navigate to the interview session page with the form parameters
    setIsSessionActive(true);
    
    // Hide the sidebar for better fullscreen experience
    dispatch(toggleSidebar());
  };
  
  const endInterviewSession = () => {
    // Show the sidebar again when exiting interview session
    dispatch(toggleSidebar());
    
    // Deactivate the interview session
    setIsSessionActive(false);
    
    // Reset the form
    setShowForm(false);
  };

  // Add a class to body when session is active to control layout
  useEffect(() => {
    if (isSessionActive) {
      document.body.classList.add('full-page-interview');
    } else {
      document.body.classList.remove('full-page-interview');
    }
    
    return () => {
      document.body.classList.remove('full-page-interview');
    };
  }, [isSessionActive]);

  // Add useEffect to clear customRole when a non-other role is selected
  useEffect(() => {
    if (selectedRole !== 'other') {
      setCustomRole('');
    }
  }, [selectedRole]);

  if (isSessionActive) {
    // Get the selected resume filename if available
    const selectedResumeFile = userResumes.find(resume => resume.id === selectedResume)?.fileName || '';
    
    // Define a function to get the role label from the value
    const getRoleLabel = (roleValue: string): string => {
      if (roleValue === 'other') {
        return customRole;
      }
      
      // Find the matching role option from the searchable select options structure
      const allRoleLabels: Record<string, string> = {
        'ciso': t("Chief Information Security Officer (CISO)/Chief Security Officer (CSO)"),
        'it_director': t("IT Director"),
        'service_desk_manager': t("Service Desk Manager"),
        // Add more mappings as needed...
        // We don't need to define all of them since we can fallback to the roleValue
      };
      
      return allRoleLabels[roleValue] || roleValue;
    };
    
    // Get the display role
    const displayRole = getRoleLabel(selectedRole);
    
    return (
      <div className="interview-session-container h-screen w-screen bg-white dark:bg-gray-900">
        <InterviewSession 
          resumeId={selectedResumeId}
          jobDescription={jobDescription}
          selectedRole={selectedRole === 'other' ? customRole : selectedRole}
          onExit={endInterviewSession}
        />
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="mb-5">
        <h2 className="text-2xl font-bold">{t("Live Interview")}</h2>
        <p className="text-gray-500">{t("Practice your interview skills with AI")}</p>
      </div>

      {!showForm ? (
        <>
          <div className="mb-6 flex justify-center">
            <button
              onClick={toggleForm}
              className="btn btn-primary rounded-xl border-0 bg-gradient-to-r from-primary to-secondary py-3 px-6 text-lg shadow-none"
            >
              {t("General Interview")}
            </button>
          </div>

          <div className="panel">
            <div className="mb-5">
              <h5 className="text-lg font-semibold dark:text-white-light">
                {t("Previous Interviews")}
              </h5>
              <p className="text-gray-500">
                {t("Your past interview sessions")}
              </p>
            </div>

            {previousInterviews.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead>
                    <tr>
                      <th className="border-b border-gray-200 px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider dark:border-gray-700">
                        {t("Interview Title")}
                      </th>
                      <th className="border-b border-gray-200 px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider dark:border-gray-700">
                        {t("Date")}
                      </th>
                      <th className="border-b border-gray-200 px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider dark:border-gray-700">
                        {t("Duration")}
                      </th>
                      <th className="border-b border-gray-200 px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider dark:border-gray-700">
                        {t("Actions")}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {previousInterviews.map((interview) => (
                      <tr key={interview.id} className="hover:bg-gray-100 dark:hover:bg-gray-700">
                        <td className="whitespace-nowrap px-4 py-3 text-sm">
                          {interview.title}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-sm">
                          {interview.date}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-sm">
                          {interview.duration}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-sm">
                          <button className="text-primary hover:text-secondary">
                            {t("View")}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-700 dark:bg-gray-800">
                <p className="text-gray-600 dark:text-gray-300">
                  {t("You haven't completed any interviews yet.")}
                </p>
              </div>
            )}
          </div>
        </>
      ) : (
        <div className="panel">
          <div className="mb-5">
            <h5 className="text-lg font-semibold dark:text-white-light">
              {t("Setup Your Interview")}
            </h5>
            <p className="text-gray-500">
              {t("Configure your interview settings")}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Resume Selection")}
              </label>
              <div className="flex items-center">
                <FaFileAlt className="mr-2 text-gray-400" />
                <select 
                  className="form-select w-full rounded-lg border-gray-300 px-4 py-2 dark:border-gray-600 dark:bg-gray-700"
                  value={selectedResume}
                  onChange={handleResumeChange}
                  disabled={isLoadingResumes}
                >
                  <option value="">{t("Select a resume")}</option>
                  {userResumes.length === 0 && !isLoadingResumes && (
                    <option value="" disabled>
                      {t("No resumes available. Please upload a resume in Resume Hub.")}
                    </option>
                  )}
                  {isLoadingResumes && (
                    <option value="" disabled>
                      {t("Loading resumes...")}
                    </option>
                  )}
                  {userResumes.map((resume) => (
                    <option key={resume.id} value={resume.id}>
                      {resume.fileName} ({resume.role})
                    </option>
                  ))}
                </select>
              </div>
              {userResumes.length === 0 && !isLoadingResumes && (
                <p className="mt-2 text-sm text-red-500">
                  {t("No resumes found. Please upload a resume in the Resume Hub before starting an interview.")}
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Role")}
              </label>
              <div className="flex items-center">
                <FaUserTie className="mr-2 text-gray-400" />
                <SearchableSelect
                  options={[
                    // IT Leaders group
                    { value: "ciso", label: t("Chief Information Security Officer (CISO)/Chief Security Officer (CSO)"), group: t("IT Leaders") as string },
                    { value: "it_director", label: t("IT Director"), group: t("IT Leaders") as string },
                    { value: "service_desk_manager", label: t("Service Desk Manager"), group: t("IT Leaders") as string },
                    
                    // Architecture group
                    { value: "cloud_architect", label: t("Cloud Infrastructure Architect"), group: t("Architecture") as string },
                    { value: "enterprise_architect", label: t("Enterprise Architect"), group: t("Architecture") as string },
                    { value: "it_systems_architect", label: t("IT Systems Architect"), group: t("Architecture") as string },
                    { value: "solutions_architect", label: t("Solutions Architect"), group: t("Architecture") as string },
                    { value: "technical_architect", label: t("Technical Architect"), group: t("Architecture") as string },
                    
                    // Network and System Administration group
                    { value: "computer_systems_manager", label: t("Computer Systems Manager"), group: t("Network and System Administration") as string },
                    { value: "network_architect", label: t("Network Architect"), group: t("Network and System Administration") as string },
                    { value: "systems_analyst", label: t("Systems Analyst"), group: t("Network and System Administration") as string },
                    { value: "it_coordinator", label: t("IT Coordinator"), group: t("Network and System Administration") as string },
                    { value: "network_admin", label: t("Network Administrator"), group: t("Network and System Administration") as string },
                    { value: "network_engineer", label: t("Network Engineer"), group: t("Network and System Administration") as string },
                    { value: "service_desk_analyst", label: t("Service Desk Analyst"), group: t("Network and System Administration") as string },
                    { value: "sysadmin", label: t("System Administrator (Sysadmin)"), group: t("Network and System Administration") as string },
                    { value: "windows_admin", label: t("Windows Administrator"), group: t("Network and System Administration") as string },
                    { value: "vmware_engineer", label: t("VMware Engineer"), group: t("Network and System Administration") as string },
                    { value: "wireless_network_engineer", label: t("Wireless Network Engineer"), group: t("Network and System Administration") as string },
                    
                    // Database Administration group
                    { value: "database_admin", label: t("Database Administrator"), group: t("Database Administration") as string },
                    { value: "database_analyst", label: t("Database Analyst"), group: t("Database Administration") as string },
                    { value: "data_quality_manager", label: t("Data Quality Manager"), group: t("Database Administration") as string },
                    { value: "database_report_writer", label: t("Database Report Writer"), group: t("Database Administration") as string },
                    { value: "sql_admin", label: t("SQL Database Administrator"), group: t("Database Administration") as string },
                    
                    // Business Analysis and BI group
                    { value: "big_data_engineer", label: t("Big Data Engineer/Architect"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "bi_analyst", label: t("Business Intelligence Specialist/Analyst"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "business_systems_analyst", label: t("Business Systems Analyst"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "data_analyst", label: t("Data Analyst"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "data_analytics_developer", label: t("Data Analytics Developer"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "data_modeling_analyst", label: t("Data Modeling Analyst"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "data_scientist", label: t("Data Scientist"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "azure_data_engineer", label: t("Azure Data Engineer"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "azure_data_factory", label: t("Azure Data Factory Specialist"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "azure_databricks", label: t("Azure Databricks Specialist"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "data_warehouse_manager", label: t("Data Warehouse Manager"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "data_warehouse_specialist", label: t("Data Warehouse Programming Specialist"), group: t("Business Analysis and Business Intelligence") as string },
                    { value: "intelligence_specialist", label: t("Intelligence Specialist"), group: t("Business Analysis and Business Intelligence") as string },
                    
                    // Software Development group
                    { value: "backend", label: t("Back-End Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "cloud_architect", label: t("Cloud/Software Architect"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "cloud_developer", label: t("Cloud/Software Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "cloud_engineer", label: t("Cloud/Software Applications Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "cloud_sysadmin", label: t("Cloud System Administrator"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "cloud_system_engineer", label: t("Cloud System Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "devops", label: t("DevOps Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "frontend", label: t("Front-End Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "fullstack", label: t("Full Stack Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "java_developer", label: t("Java Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "dotnet_developer", label: t(".NET Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "platform_engineer", label: t("Platform Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "release_manager", label: t("Release Manager"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "reliability_engineer", label: t("Reliability Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "software_engineer", label: t("Software Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "qa_analyst", label: t("Software Quality Assurance Analyst (QA)"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "automation_test_engineer", label: t("Automation Test Engineer"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "tosca_automation_tester", label: t("Tosca Automation Tester"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "ui_designer", label: t("UI Designer (User Interface Designer)"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "ux_designer", label: t("UX Designer (User Experience Designer)"), group: t("Software Development, DevOps, and Cloud") as string },
                    { value: "web_developer", label: t("Web Developer"), group: t("Software Development, DevOps, and Cloud") as string },
                    
                    // Security group
                    { value: "app_security_admin", label: t("Application Security Administrator"), group: t("Security") as string },
                    { value: "ai_security_specialist", label: t("Artificial Intelligence Security Specialist"), group: t("Security") as string },
                    { value: "cloud_security_specialist", label: t("Cloud Security Specialist"), group: t("Security") as string },
                    { value: "cybersecurity_hardware_engineer", label: t("Cybersecurity Hardware Engineer"), group: t("Security") as string },
                    { value: "cybersecurity_specialist", label: t("Cybersecurity Specialist"), group: t("Security") as string },
                    { value: "cyberintelligence_specialist", label: t("Cyberintelligence Specialist"), group: t("Security") as string },
                    { value: "cryptographer", label: t("Cryptographer"), group: t("Security") as string },
                    { value: "data_privacy_officer", label: t("Data Privacy Officer"), group: t("Security") as string },
                    { value: "digital_forensics_analyst", label: t("Digital Forensics Analyst"), group: t("Security") as string },
                    { value: "it_security_engineer", label: t("IT Security Engineer"), group: t("Security") as string },
                    { value: "information_assurance_analyst", label: t("Information Assurance Analyst"), group: t("Security") as string },
                    { value: "security_systems_admin", label: t("Security Systems Administrator"), group: t("Security") as string },
                    { value: "aml_kyc_analyst", label: t("AML/KYC Analyst"), group: t("Security") as string },
                    
                    // Help Desk group
                    { value: "help_desk_specialist", label: t("Help Desk Support Specialist"), group: t("Help Desk and Customer Support") as string },
                    { value: "it_support_specialist", label: t("IT Support Specialist"), group: t("Help Desk and Customer Support") as string },
                    { value: "technical_support_engineer", label: t("Technical Support Engineer"), group: t("Help Desk and Customer Support") as string },
                    { value: "customer_service", label: t("Customer Service Representative"), group: t("Help Desk and Customer Support") as string },
                    
                    // Project Management group
                    { value: "technical_product_manager", label: t("Technical Product Manager"), group: t("Project and Product Management") as string },
                    { value: "product", label: t("Product Manager"), group: t("Project and Product Management") as string },
                    { value: "project_manager", label: t("Project Manager"), group: t("Project and Product Management") as string },
                    { value: "program_manager", label: t("Program Manager"), group: t("Project and Product Management") as string },
                    { value: "portfolio_manager", label: t("Portfolio Manager"), group: t("Project and Product Management") as string },
                    
                    // SAP Specialists group
                    { value: "sap_professional", label: t("SAP Professionals (All Modules)"), group: t("SAP Specialists") as string },
                    { value: "sap_fico", label: t("SAP FICO Consultant"), group: t("SAP Specialists") as string },
                    { value: "sap_mm", label: t("SAP MM Consultant"), group: t("SAP Specialists") as string },
                    { value: "sap_abap", label: t("SAP ABAP Developer"), group: t("SAP Specialists") as string },
                    { value: "sap_basis", label: t("SAP Basis Administrator"), group: t("SAP Specialists") as string },
                    
                    // Other Specialist Roles group
                    { value: "us_mortgage_specialist", label: t("US Mortgage Specialist"), group: t("Other Specialist Roles") as string },
                    
                    // Custom/Other option
                    { value: "other", label: t("Other (specify)"), group: t("Custom") as string },
                  ]}
                  value={selectedRole}
                  onChange={setSelectedRole}
                  placeholder={t("Select a role") as string}
                  searchPlaceholder={t("Search roles...") as string}
                  icon={<FaUserTie className="h-4 w-4 text-gray-500" />}
                />
              </div>
            </div>

            {selectedRole === "other" && (
              <div className="mt-3">
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("Specify Role")}
                </label>
                <div className="flex items-center">
                  <FaPen className="mr-2 text-gray-400" />
                  <input
                    type="text"
                    className="form-input w-full rounded-lg border-gray-300 px-4 py-2 dark:border-gray-600 dark:bg-gray-700"
                    value={customRole}
                    onChange={(e) => setCustomRole(e.target.value)}
                    placeholder={t("Enter the specific role") as string}
                  />
                </div>
              </div>
            )}

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Job Description")}
              </label>
              <div className="flex items-center">
                <FaFileContract className="mr-2 text-gray-400" />
                <textarea
                  className="form-textarea w-full rounded-lg border-gray-300 px-4 py-2 dark:border-gray-600 dark:bg-gray-700"
                  rows={4}
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  placeholder={t("Paste the job description here") as string}
                ></textarea>
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Interview Type")}
              </label>
              <div className="flex items-center">
                <FaList className="mr-2 text-gray-400" />
                <select 
                  className="form-select w-full rounded-lg border-gray-300 px-4 py-2 dark:border-gray-600 dark:bg-gray-700"
                  value={selectedInterviewType}
                  onChange={(e) => setSelectedInterviewType(e.target.value)}
                >
                  <option value="">{t("Select an interview type")}</option>
                  <option value="behavioral">{t("Behavioral")}</option>
                  <option value="technical">{t("Technical")}</option>
                  <option value="case_study">{t("Case Study")}</option>
                  <option value="mixed">{t("Mixed")}</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={toggleForm}
                className="btn btn-outline-danger rounded-xl border-2 px-5 py-2"
              >
                {t("Cancel")}
              </button>
              <button
                type="submit"
                className="btn btn-primary rounded-xl border-0 bg-gradient-to-r from-primary to-secondary px-5 py-2 shadow-none"
                disabled={userResumes.length === 0 || !selectedResume}
              >
                {t("Launch")}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
} 