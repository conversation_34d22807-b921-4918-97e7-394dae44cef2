'use client';

import { useEffect, useRef } from 'react';
import { useMediaStream } from '@/hooks/use-media-stream';

export default function VideoStream() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { stream, error } = useMediaStream();

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);

  if (error) {
    return (
      <div className="aspect-video bg-secondary rounded-lg flex items-center justify-center">
        <p className="text-destructive">Failed to access camera: {error.message}</p>
      </div>
    );
  }

  return (
    <video
      ref={videoRef}
      autoPlay
      playsInline
      muted
      className="w-full h-full rounded-lg object-cover"
    />
  );
}