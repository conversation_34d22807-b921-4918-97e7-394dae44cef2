/* Add these styles at the end of the file */

/* Full-page interview styles */
body.full-page-interview .sidebar {
  display: none !important;
}

body.full-page-interview .main-content {
  margin-left: 0 !important;
  width: 100% !important;
  padding: 0 !important;
  max-width: 100% !important;
}

body.full-page-interview header {
  display: none !important;
}

/* Enhanced footer and copyright hiding */
body.full-page-interview footer,
body.full-page-interview .minimal-footer,
body.full-page-interview [class*="footer"],
body.full-page-interview div:has(> .copyright),
body.full-page-interview .copyright,
body.full-page-interview *:has(> span:contains("© 2025")),
body.full-page-interview *:has(> div:contains("© 2025")),
body.full-page-interview *:has(> p:contains("© 2025")),
body.full-page-interview *:has(> small:contains("© 2025")),
body.full-page-interview *:has(> *:contains("All rights reserved")),
body.full-page-interview p:has(> span:contains("Copyright")),
body.full-page-interview div:has(> span:contains("Copyright")) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  z-index: -1 !important;
}

body.full-page-interview .interview-session-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 999 !important;
  overflow: hidden !important;
  padding: 8px !important;
  margin: 0 !important;
  border-radius: 0 !important;
  box-sizing: border-box !important;
}

/* Remove container width restrictions and padding */
body.full-page-interview .container {
  max-width: 100% !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Remove padding from main container */
body.full-page-interview .min-h-screen {
  padding: 0 !important;
  margin: 0 !important;
  min-height: 100vh !important;
  height: 100vh !important;
  overflow: hidden !important;
}

/* Remove grid gap */
body.full-page-interview .grid {
  gap: 0 !important;
  grid-gap: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  height: calc(100vh - 16px) !important;
  width: calc(100vw - 16px) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

/* Remove card padding */
body.full-page-interview .card,
body.full-page-interview div[class*="card"],
body.full-page-interview div[class*="Card"] {
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
  overflow: hidden !important;
}

/* Zero out all possible margins and paddings */
body.full-page-interview h1,
body.full-page-interview h2,
body.full-page-interview h3,
body.full-page-interview h4,
body.full-page-interview h5,
body.full-page-interview h6 {
  padding: 8px !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

/* Right panel styling */
body.full-page-interview .right-panel {
  border-left: none !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: white !important;
}

body.full-page-interview.dark .right-panel {
  background-color: #1e1e2d !important;
}

/* Interview session heading styles */
body.full-page-interview h2 {
  margin: 0 !important;
  padding: 8px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

body.full-page-interview.dark h2 {
  border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

/* Fix scrollable areas */
body.full-page-interview .scrollarea,
body.full-page-interview div[class*="scroll"],
body.full-page-interview div[class*="Scroll"] {
  height: calc(100vh - 54px) !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix all child elements */
body.full-page-interview .interview-session-container * {
  box-sizing: border-box !important;
}

/* Fix window capture container */
body.full-page-interview .flex-1 {
  flex: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
  background-color: white !important;
}

body.full-page-interview.dark .flex-1 {
  background-color: #1e1e2d !important;
}

.interview-session-container .back-button {
  position: absolute !important;
  top: 1rem !important;
  left: 1rem !important;
  z-index: 1000 !important;
}

/* Minimal footer styles */
.minimal-footer {
  padding: 0.5rem 0;
  height: auto;
  font-size: 0.875rem;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .minimal-footer {
  border-top-color: rgba(255, 255, 255, 0.1);
}

/* Resizable columns styling */
body.full-page-interview .resizer-handle {
  transition: background-color 0.2s ease;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

body.full-page-interview.dark .resizer-handle {
  border-left-color: rgba(255, 255, 255, 0.1);
  border-right-color: rgba(255, 255, 255, 0.1);
}

body.full-page-interview .resizer-handle:hover {
  background-color: rgba(63, 81, 181, 0.2) !important;
}

body.full-page-interview .resizer-grip {
  transition: color 0.2s ease;
  opacity: 0.5;
}

body.full-page-interview .resizer-handle:hover .resizer-grip {
  opacity: 1;
  color: #3f51b5 !important;
}

body.full-page-interview .resizer-grip.active {
  opacity: 1;
}

/* Prevent text selection while resizing */
body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

body.resizing * {
  user-select: none !important;
  -webkit-user-select: none !important;
}

/* Meeting room button animation */
@keyframes float-button {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

/* Also keep original keyframes for backward compatibility */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-shadow {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

/* Standalone direct button animation class */
.animated-connect-button {
  animation-name: float-button, pulse-glow !important;
  animation-duration: 3s, 2s !important;
  animation-timing-function: ease-in-out, ease !important;
  animation-iteration-count: infinite, infinite !important;
  animation-direction: normal, normal !important;
  animation-fill-mode: both, both !important;
  animation-play-state: running, running !important;
  animation-delay: 0s, 0s !important;
  
  position: relative !important;
  z-index: 100 !important;
  transform-origin: center !important;
  box-shadow: 0 0 15px rgba(79, 70, 229, 0.5) !important;
  transition: all 0.3s ease !important;
}

.animated-connect-button:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.8) !important;
}

/* Preserve original classes for backward compatibility */
.connect-meeting-button {
  animation: float 3s ease-in-out infinite, pulse-shadow 2s infinite !important;
  position: relative !important;
  z-index: 10 !important;
  transform-origin: center !important;
}

/* Enhanced animation for the button hover state */
.connect-meeting-button:hover {
  animation: pulse-shadow 1s infinite !important;
  transform: scale(1.05) !important;
  transition: transform 0.3s ease !important;
}