import { S3Client } from '@aws-sdk/client-s3';
import multerS3 from 'multer-s3';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { PrismaClient } from '../helpers/functions';
import { coreConstant } from '../helpers/coreConstant';
import path from 'path';

export const validImageUploadTypesRegex = /jpeg|jpg|png|gif|bmp|webp/;
export const maxImageUploadSize = 3 * 1024 * 1024;

// Create S3 client instance
const s3 = new S3Client({
  region: process.env.AWS_REGION || 'ap-south-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
});

// Make sure we have the bucket name
const bucketName = process.env.AWS_S3_BUCKET_NAME || 'hiremage-backend';
const s3UrlPrefix = process.env.AWS_S3_URL_PREFIX || 'https://hiremage-backend.s3.amazonaws.com';

export const s3UploadConfig: MulterOptions = {
  storage: multerS3({
    s3: s3,
    bucket: bucketName,
    acl: 'public-read',
    contentType: multerS3.AUTO_CONTENT_TYPE,
    key: (request, file, callback) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      const user: any = request.user;
      const { originalname, mimetype } = file;
      const fileName = `${uniqueSuffix}-${file.originalname}`;
      
      // Create a record in the database
      PrismaClient.myUploads
        .create({
          data: {
            user: {
              connect: { id: user.id },
            },
            fieldname: originalname,
            mimetype: mimetype,
            originalname: originalname,
            file_path: `${s3UrlPrefix}/${fileName}`, // S3 URL
            filename: fileName,
            storage_type: 's3',
          } as any, // Use any to bypass TypeScript error until Prisma types are regenerated
        })
        .then((res) => {
          console.log(res, 'File uploaded to S3');
        });

      callback(null, fileName);
    },
  }),

  fileFilter: (request, file, callback) => {
    const mimetype = validImageUploadTypesRegex.test(file.mimetype);
    const extname = validImageUploadTypesRegex.test(
      path.extname(file.originalname).toLowerCase(),
    );

    if (mimetype && extname) {
      return callback(null, true);
    }

    return callback(new Error('Invalid file type'), false);
  },

  limits: {
    fileSize: maxImageUploadSize,
  },
};

// Config for audio uploads
const maxAudioUploadSize = 50 * 1024 * 1024;
export const audioS3UploadConfig: MulterOptions = {
  // Use the same S3 storage with different settings
  storage: multerS3({
    s3: s3,
    bucket: bucketName,
    acl: 'public-read',
    contentType: multerS3.AUTO_CONTENT_TYPE,
    key: (request, file, callback) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      const user: any = request.user;
      const fileName = `audio/${uniqueSuffix}-${file.originalname}`;
      
      // Create audio record in database
      PrismaClient.myUploads
        .create({
          data: {
            user: {
              connect: { id: user.id },
            },
            fieldname: file.originalname,
            mimetype: file.mimetype,
            originalname: file.originalname,
            file_path: `${s3UrlPrefix}/${fileName}`,
            filename: fileName,
            storage_type: 's3',
          } as any, // Use any to bypass TypeScript error until Prisma types are regenerated
        })
        .then((res) => {
          console.log(res, 'Audio uploaded to S3');
        });

      callback(null, fileName);
    },
  }),
  
  limits: {
    fileSize: maxAudioUploadSize,
  },
}; 