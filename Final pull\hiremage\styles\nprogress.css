/* Make clicks pass-through */
#nprogress {
  /* --primary-color: #fcd535; */
  pointer-events: none;
}

#nprogress .bar {
  background: #524eb7;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
}

/* Fancy blur effect */
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #524eb7, 0 0 5px #524eb7;
  opacity: 1;

  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
#nprogress {
  background: transparent !important;
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999;
}
#nprogress .spinner {
  display: none;
  position: fixed;
  z-index: 1031;
  top: 50%;
  right: 0;
  left: 0;
  margin: 0 auto;
}

#nprogress .spinner-icon {
  width: 6rem;
  height: 6rem;
  box-sizing: border-box;
  border: solid 2px transparent;
  border-top-color: var(--text-color-3);
  border-left-color: var(--text-color-3);
  border-radius: 50%;
  z-index: 1031;
  top: 50%;
  right: 0;
  left: 0;
  margin: 0 auto;

  -webkit-animation: nprogress-spinner 400ms linear infinite;
  animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes nprogress-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
