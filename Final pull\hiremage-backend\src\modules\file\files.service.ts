import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';
import {
  addPhotoPrefix,
  errorResponse,
  processException,
  successResponse,
} from 'src/shared/helpers/functions';
import { ResponseModel } from 'src/shared/models/response.model';
import { coreConstant } from 'src/shared/helpers/coreConstant';
import path from 'path';
import { Response } from 'express';
import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class FilesService {
  private s3Client: S3Client;

  constructor(private readonly prisma: PrismaService) {
    // Initialize S3 client
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  async getMyUploadedFiles(user: User): Promise<ResponseModel> {
    try {
      const myFiles = await this.prisma.myUploads.findMany({
        where: {
          user_id: user.id,
        },
      });

      if (!myFiles || myFiles.length === 0) {
        return errorResponse('No files found');
      }

      // Map through the myFiles array - handle both local and S3 files
      const filesWithPrefix = myFiles.map((file) => {
        // Make sure we have the storage_type field, default to 'local' if not present
        const storageType = file.storage_type || 'local';
        
        return {
          ...file,
          file_path: storageType === 's3' 
            ? file.file_path // S3 URLs are already complete
            : addPhotoPrefix(file.file_path), // Local files need prefix
        };
      });

      return successResponse('Files retrieved successfully', filesWithPrefix);
    } catch (error) {
      processException(error);
    }
  }

  async serveFile(filename: string, res: Response) {
    try {
      // Look up the file in database
      const fileRecord = await this.prisma.myUploads.findFirst({
        where: { filename },
      });

      if (!fileRecord) {
        return res.status(404).send('File not found');
      }

      // Handle based on storage type
      if (fileRecord.storage_type === 's3') {
        // For S3 storage, simply redirect to the public URL
        if (fileRecord.file_path && fileRecord.file_path.startsWith('http')) {
          return res.redirect(fileRecord.file_path);
        }
        
        // Fallback to the standard S3 URL format if file_path doesn't contain a full URL
        const s3Url = `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${filename}`;
        return res.redirect(s3Url);
      } else {
        // For local storage, serve from local filesystem
        return res.sendFile(filename, {
          root: path.resolve(`./${coreConstant.FILE_DESTINATION}`),
        });
      }
    } catch (error) {
      console.error('Error serving file:', error);
      return res.status(500).send('Error serving file');
    }
  }

  createUploadResponse(file: Express.Multer.File) {
    if (!file) {
      return errorResponse('File upload failed');
    }
    
    return successResponse('File uploaded successfully', {
      filename: file.filename,
      originalname: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      path: file['location'] || file.path, // S3 provides location, local provides path
    });
  }
}
